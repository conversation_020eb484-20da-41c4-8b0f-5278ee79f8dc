# Project-Specific Rules and Architecture

## Core System References
- Always refer to `eots_system_guide_v2_5.md` for everything related to the Elite Options System
- Refer to `memory-bank/elite-ai-blueprint.md` for AI Blueprint architecture and components
- Use branch-specific memory bank files for context (see `memory-bank/BRANCH_INDEX.md`)

## Architecture Separation (Updated 2025-06-14)

### Elite AI Blueprint Independence
- **Location**: `elite-ai-blueprint/` directory
- **Purpose**: Completely independent AI infrastructure
- **Key Components**:
  - Timestamp Management: `elite-ai-blueprint/utilities/timestamp-management/`
  - MCP Servers: `elite-ai-blueprint/mcp-servers/`
  - Cognitive Systems: `elite-ai-blueprint/cognitive-systems/`
  - **MCP Tools Arsenal**: Omni-potent capabilities across 7+ domains
    - Temporal Intelligence (Time MCP)
    - Design-Code Bridge (Figma AI Bridge)
    - Development Orchestration (GitHub MCP)
    - High-Performance Caching (Redis MCP)
    - Real-Time Intelligence (HotNews Server)
    - Web Automation Engine (Puppeteer)
    - Advanced Cognitive Systems (Knowledge Graph, Sequential Thinking, Memory, etc.)
- **Integration**: No direct dependencies on Elite Options System

### System Boundaries
- **Elite Options System**: Trading-focused, market data, options analysis
- **Elite AI Blueprint**: AI-focused, cognitive systems, memory management
- **Communication**: Well-defined APIs and event-driven coordination
- **Timestamp Management**: AI Blueprint handles AI timing, separate from trading timing

## Development Guidelines
1. Maintain clean separation between trading and AI systems
2. Use appropriate memory bank branch for context
3. Follow timestamp management protocols for AI components
4. Document architectural changes in memory bank
5. Test integration points between separated systems

[x]: # this file is for rules that are specific to the project
[]: # it should be used as a reference for the project
[]: # it should not be used as a guide for the project