/* =============================================================================
   ELITE OPTIONS TRADING SYSTEM - UTILITY CLASSES
   Helper classes for spacing, positioning, typography, and common styling
============================================================================= */

/* ==========================================================================
   SPACING UTILITIES
========================================================================== */

/* Margin utilities */
.m-0 { margin: 0; }
.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }
.m-2xl { margin: var(--space-2xl); }
.m-3xl { margin: var(--space-3xl); }
.m-auto { margin: auto; }

/* Margin X-axis */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: var(--space-xs); margin-right: var(--space-xs); }
.mx-sm { margin-left: var(--space-sm); margin-right: var(--space-sm); }
.mx-md { margin-left: var(--space-md); margin-right: var(--space-md); }
.mx-lg { margin-left: var(--space-lg); margin-right: var(--space-lg); }
.mx-xl { margin-left: var(--space-xl); margin-right: var(--space-xl); }
.mx-2xl { margin-left: var(--space-2xl); margin-right: var(--space-2xl); }
.mx-3xl { margin-left: var(--space-3xl); margin-right: var(--space-3xl); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Margin Y-axis */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: var(--space-xs); margin-bottom: var(--space-xs); }
.my-sm { margin-top: var(--space-sm); margin-bottom: var(--space-sm); }
.my-md { margin-top: var(--space-md); margin-bottom: var(--space-md); }
.my-lg { margin-top: var(--space-lg); margin-bottom: var(--space-lg); }
.my-xl { margin-top: var(--space-xl); margin-bottom: var(--space-xl); }
.my-2xl { margin-top: var(--space-2xl); margin-bottom: var(--space-2xl); }
.my-3xl { margin-top: var(--space-3xl); margin-bottom: var(--space-3xl); }

/* Individual margin sides */
.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }
.mt-3xl { margin-top: var(--space-3xl); }
.mt-auto { margin-top: auto; }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: var(--space-xs); }
.mr-sm { margin-right: var(--space-sm); }
.mr-md { margin-right: var(--space-md); }
.mr-lg { margin-right: var(--space-lg); }
.mr-xl { margin-right: var(--space-xl); }
.mr-2xl { margin-right: var(--space-2xl); }
.mr-3xl { margin-right: var(--space-3xl); }
.mr-auto { margin-right: auto; }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }
.mb-3xl { margin-bottom: var(--space-3xl); }
.mb-auto { margin-bottom: auto; }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: var(--space-xs); }
.ml-sm { margin-left: var(--space-sm); }
.ml-md { margin-left: var(--space-md); }
.ml-lg { margin-left: var(--space-lg); }
.ml-xl { margin-left: var(--space-xl); }
.ml-2xl { margin-left: var(--space-2xl); }
.ml-3xl { margin-left: var(--space-3xl); }
.ml-auto { margin-left: auto; }

/* Padding utilities */
.p-0 { padding: 0; }
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-2xl { padding: var(--space-2xl); }
.p-3xl { padding: var(--space-3xl); }

/* Padding X-axis */
.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: var(--space-xs); padding-right: var(--space-xs); }
.px-sm { padding-left: var(--space-sm); padding-right: var(--space-sm); }
.px-md { padding-left: var(--space-md); padding-right: var(--space-md); }
.px-lg { padding-left: var(--space-lg); padding-right: var(--space-lg); }
.px-xl { padding-left: var(--space-xl); padding-right: var(--space-xl); }
.px-2xl { padding-left: var(--space-2xl); padding-right: var(--space-2xl); }
.px-3xl { padding-left: var(--space-3xl); padding-right: var(--space-3xl); }

/* Padding Y-axis */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: var(--space-xs); padding-bottom: var(--space-xs); }
.py-sm { padding-top: var(--space-sm); padding-bottom: var(--space-sm); }
.py-md { padding-top: var(--space-md); padding-bottom: var(--space-md); }
.py-lg { padding-top: var(--space-lg); padding-bottom: var(--space-lg); }
.py-xl { padding-top: var(--space-xl); padding-bottom: var(--space-xl); }
.py-2xl { padding-top: var(--space-2xl); padding-bottom: var(--space-2xl); }
.py-3xl { padding-top: var(--space-3xl); padding-bottom: var(--space-3xl); }

/* Individual padding sides */
.pt-0 { padding-top: 0; }
.pt-xs { padding-top: var(--space-xs); }
.pt-sm { padding-top: var(--space-sm); }
.pt-md { padding-top: var(--space-md); }
.pt-lg { padding-top: var(--space-lg); }
.pt-xl { padding-top: var(--space-xl); }
.pt-2xl { padding-top: var(--space-2xl); }
.pt-3xl { padding-top: var(--space-3xl); }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: var(--space-xs); }
.pr-sm { padding-right: var(--space-sm); }
.pr-md { padding-right: var(--space-md); }
.pr-lg { padding-right: var(--space-lg); }
.pr-xl { padding-right: var(--space-xl); }
.pr-2xl { padding-right: var(--space-2xl); }
.pr-3xl { padding-right: var(--space-3xl); }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: var(--space-xs); }
.pb-sm { padding-bottom: var(--space-sm); }
.pb-md { padding-bottom: var(--space-md); }
.pb-lg { padding-bottom: var(--space-lg); }
.pb-xl { padding-bottom: var(--space-xl); }
.pb-2xl { padding-bottom: var(--space-2xl); }
.pb-3xl { padding-bottom: var(--space-3xl); }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: var(--space-xs); }
.pl-sm { padding-left: var(--space-sm); }
.pl-md { padding-left: var(--space-md); }
.pl-lg { padding-left: var(--space-lg); }
.pl-xl { padding-left: var(--space-xl); }
.pl-2xl { padding-left: var(--space-2xl); }
.pl-3xl { padding-left: var(--space-3xl); }

/* ==========================================================================
   TYPOGRAPHY UTILITIES
========================================================================== */

/* Font sizes */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }
.text-6xl { font-size: var(--text-6xl); }

/* Font weights */
.font-thin { font-weight: var(--font-thin); }
.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
.font-extrabold { font-weight: var(--font-extrabold); }
.font-black { font-weight: var(--font-black); }

/* Font families */
.font-sans { font-family: var(--font-sans); }
.font-mono { font-family: var(--font-mono); }

/* Text alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Text colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--accent-primary); }
.text-positive { color: var(--positive); }
.text-negative { color: var(--negative); }
.text-warning { color: var(--warning); }
.text-white { color: white; }
.text-black { color: black; }

/* Text decoration */
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

/* Text transform */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* Letter spacing */
.tracking-tighter { letter-spacing: var(--tracking-tighter); }
.tracking-tight { letter-spacing: var(--tracking-tight); }
.tracking-normal { letter-spacing: var(--tracking-normal); }
.tracking-wide { letter-spacing: var(--tracking-wide); }
.tracking-wider { letter-spacing: var(--tracking-wider); }
.tracking-widest { letter-spacing: var(--tracking-widest); }

/* Line height */
.leading-none { line-height: var(--leading-none); }
.leading-tight { line-height: var(--leading-tight); }
.leading-snug { line-height: var(--leading-snug); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }
.leading-loose { line-height: var(--leading-loose); }

/* Text overflow */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

/* White space */
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

/* Word break */
.break-normal { word-break: normal; overflow-wrap: normal; }
.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

/* ==========================================================================
   DISPLAY UTILITIES
========================================================================== */

.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.inline-grid { display: inline-grid; }
.table { display: table; }
.table-cell { display: table-cell; }
.table-row { display: table-row; }
.hidden { display: none; }

/* ==========================================================================
   POSITION UTILITIES
========================================================================== */

.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* Position values */
.top-0 { top: 0; }
.top-xs { top: var(--space-xs); }
.top-sm { top: var(--space-sm); }
.top-md { top: var(--space-md); }
.top-lg { top: var(--space-lg); }
.top-xl { top: var(--space-xl); }
.top-auto { top: auto; }
.top-full { top: 100%; }
.top-1\/2 { top: 50%; }

.right-0 { right: 0; }
.right-xs { right: var(--space-xs); }
.right-sm { right: var(--space-sm); }
.right-md { right: var(--space-md); }
.right-lg { right: var(--space-lg); }
.right-xl { right: var(--space-xl); }
.right-auto { right: auto; }
.right-full { right: 100%; }
.right-1\/2 { right: 50%; }

.bottom-0 { bottom: 0; }
.bottom-xs { bottom: var(--space-xs); }
.bottom-sm { bottom: var(--space-sm); }
.bottom-md { bottom: var(--space-md); }
.bottom-lg { bottom: var(--space-lg); }
.bottom-xl { bottom: var(--space-xl); }
.bottom-auto { bottom: auto; }
.bottom-full { bottom: 100%; }
.bottom-1\/2 { bottom: 50%; }

.left-0 { left: 0; }
.left-xs { left: var(--space-xs); }
.left-sm { left: var(--space-sm); }
.left-md { left: var(--space-md); }
.left-lg { left: var(--space-lg); }
.left-xl { left: var(--space-xl); }
.left-auto { left: auto; }
.left-full { left: 100%; }
.left-1\/2 { left: 50%; }

/* Inset utilities */
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-x-0 { left: 0; right: 0; }
.inset-y-0 { top: 0; bottom: 0; }

/* ==========================================================================
   Z-INDEX UTILITIES
========================================================================== */

.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }
.z-dropdown { z-index: var(--z-dropdown); }
.z-sticky { z-index: var(--z-sticky); }
.z-header { z-index: var(--z-header); }
.z-sidebar { z-index: var(--z-sidebar); }
.z-overlay { z-index: var(--z-overlay); }
.z-modal { z-index: var(--z-modal); }
.z-tooltip { z-index: var(--z-tooltip); }
.z-max { z-index: var(--z-max); }

/* ==========================================================================
   WIDTH AND HEIGHT UTILITIES
========================================================================== */

/* Width */
.w-0 { width: 0; }
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-min { width: min-content; }
.w-max { width: max-content; }
.w-fit { width: fit-content; }

.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-2\/4 { width: 50%; }
.w-3\/4 { width: 75%; }
.w-1\/5 { width: 20%; }
.w-2\/5 { width: 40%; }
.w-3\/5 { width: 60%; }
.w-4\/5 { width: 80%; }
.w-1\/6 { width: 16.666667%; }
.w-5\/6 { width: 83.333333%; }

/* Height */
.h-0 { height: 0; }
.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-min { height: min-content; }
.h-max { height: max-content; }
.h-fit { height: fit-content; }

.h-1\/2 { height: 50%; }
.h-1\/3 { height: 33.333333%; }
.h-2\/3 { height: 66.666667%; }
.h-1\/4 { height: 25%; }
.h-2\/4 { height: 50%; }
.h-3\/4 { height: 75%; }
.h-1\/5 { height: 20%; }
.h-2\/5 { height: 40%; }
.h-3\/5 { height: 60%; }
.h-4\/5 { height: 80%; }
.h-1\/6 { height: 16.666667%; }
.h-5\/6 { height: 83.333333%; }

/* Min/Max width */
.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.min-w-min { min-width: min-content; }
.min-w-max { min-width: max-content; }
.min-w-fit { min-width: fit-content; }

.max-w-0 { max-width: 0; }
.max-w-none { max-width: none; }
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }
.max-w-min { max-width: min-content; }
.max-w-max { max-width: max-content; }
.max-w-fit { max-width: fit-content; }
.max-w-prose { max-width: 65ch; }
.max-w-screen-sm { max-width: 640px; }
.max-w-screen-md { max-width: 768px; }
.max-w-screen-lg { max-width: 1024px; }
.max-w-screen-xl { max-width: 1280px; }
.max-w-screen-2xl { max-width: 1536px; }

/* Min/Max height */
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }
.min-h-min { min-height: min-content; }
.min-h-max { min-height: max-content; }
.min-h-fit { min-height: fit-content; }

.max-h-0 { max-height: 0; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }
.max-h-min { max-height: min-content; }
.max-h-max { max-height: max-content; }
.max-h-fit { max-height: fit-content; }

/* ==========================================================================
   BACKGROUND UTILITIES
========================================================================== */

.bg-transparent { background-color: transparent; }
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-accent { background-color: var(--accent-primary); }
.bg-positive { background-color: var(--positive); }
.bg-negative { background-color: var(--negative); }
.bg-warning { background-color: var(--warning); }
.bg-white { background-color: white; }
.bg-black { background-color: black; }

/* Background alpha variants */
.bg-accent-alpha { background-color: var(--accent-primary-alpha); }
.bg-positive-alpha { background-color: var(--positive-alpha); }
.bg-negative-alpha { background-color: var(--negative-alpha); }
.bg-warning-alpha { background-color: var(--warning-alpha); }

/* ==========================================================================
   BORDER UTILITIES
========================================================================== */

.border { border: 1px solid var(--border-primary); }
.border-0 { border: 0; }
.border-2 { border: 2px solid var(--border-primary); }
.border-4 { border: 4px solid var(--border-primary); }
.border-8 { border: 8px solid var(--border-primary); }

.border-t { border-top: 1px solid var(--border-primary); }
.border-r { border-right: 1px solid var(--border-primary); }
.border-b { border-bottom: 1px solid var(--border-primary); }
.border-l { border-left: 1px solid var(--border-primary); }

.border-t-0 { border-top: 0; }
.border-r-0 { border-right: 0; }
.border-b-0 { border-bottom: 0; }
.border-l-0 { border-left: 0; }

/* Border colors */
.border-primary { border-color: var(--border-primary); }
.border-secondary { border-color: var(--border-secondary); }
.border-accent { border-color: var(--accent-primary); }
.border-positive { border-color: var(--positive); }
.border-negative { border-color: var(--negative); }
.border-warning { border-color: var(--warning); }
.border-transparent { border-color: transparent; }

/* Border radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

.rounded-t-none { border-top-left-radius: 0; border-top-right-radius: 0; }
.rounded-r-none { border-top-right-radius: 0; border-bottom-right-radius: 0; }
.rounded-b-none { border-bottom-right-radius: 0; border-bottom-left-radius: 0; }
.rounded-l-none { border-top-left-radius: 0; border-bottom-left-radius: 0; }

.rounded-t { border-top-left-radius: var(--radius-md); border-top-right-radius: var(--radius-md); }
.rounded-r { border-top-right-radius: var(--radius-md); border-bottom-right-radius: var(--radius-md); }
.rounded-b { border-bottom-right-radius: var(--radius-md); border-bottom-left-radius: var(--radius-md); }
.rounded-l { border-top-left-radius: var(--radius-md); border-bottom-left-radius: var(--radius-md); }

/* ==========================================================================
   SHADOW UTILITIES
========================================================================== */

.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }

/* ==========================================================================
   OPACITY UTILITIES
========================================================================== */

.opacity-0 { opacity: 0; }
.opacity-5 { opacity: 0.05; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-25 { opacity: 0.25; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-75 { opacity: 0.75; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-95 { opacity: 0.95; }
.opacity-100 { opacity: 1; }

/* ==========================================================================
   CURSOR UTILITIES
========================================================================== */

.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-none { cursor: none; }
.cursor-context-menu { cursor: context-menu; }
.cursor-progress { cursor: progress; }
.cursor-cell { cursor: cell; }
.cursor-crosshair { cursor: crosshair; }
.cursor-vertical-text { cursor: vertical-text; }
.cursor-alias { cursor: alias; }
.cursor-copy { cursor: copy; }
.cursor-no-drop { cursor: no-drop; }
.cursor-grab { cursor: grab; }
.cursor-grabbing { cursor: grabbing; }
.cursor-all-scroll { cursor: all-scroll; }
.cursor-col-resize { cursor: col-resize; }
.cursor-row-resize { cursor: row-resize; }
.cursor-n-resize { cursor: n-resize; }
.cursor-e-resize { cursor: e-resize; }
.cursor-s-resize { cursor: s-resize; }
.cursor-w-resize { cursor: w-resize; }
.cursor-ne-resize { cursor: ne-resize; }
.cursor-nw-resize { cursor: nw-resize; }
.cursor-se-resize { cursor: se-resize; }
.cursor-sw-resize { cursor: sw-resize; }
.cursor-ew-resize { cursor: ew-resize; }
.cursor-ns-resize { cursor: ns-resize; }
.cursor-nesw-resize { cursor: nesw-resize; }
.cursor-nwse-resize { cursor: nwse-resize; }
.cursor-zoom-in { cursor: zoom-in; }
.cursor-zoom-out { cursor: zoom-out; }

/* ==========================================================================
   USER SELECT UTILITIES
========================================================================== */

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ==========================================================================
   POINTER EVENTS UTILITIES
========================================================================== */

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* ==========================================================================
   VISIBILITY UTILITIES
========================================================================== */

.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* ==========================================================================
   TRANSFORM UTILITIES
========================================================================== */

.transform { transform: var(--transform, none); }
.transform-none { transform: none; }

/* Translate */
.translate-x-0 { --transform-translate-x: 0px; }
.translate-x-1 { --transform-translate-x: 0.25rem; }
.translate-x-2 { --transform-translate-x: 0.5rem; }
.translate-x-3 { --transform-translate-x: 0.75rem; }
.translate-x-4 { --transform-translate-x: 1rem; }
.translate-x-5 { --transform-translate-x: 1.25rem; }
.translate-x-6 { --transform-translate-x: 1.5rem; }
.translate-x-8 { --transform-translate-x: 2rem; }
.translate-x-10 { --transform-translate-x: 2.5rem; }
.translate-x-12 { --transform-translate-x: 3rem; }
.translate-x-16 { --transform-translate-x: 4rem; }
.translate-x-20 { --transform-translate-x: 5rem; }
.translate-x-24 { --transform-translate-x: 6rem; }
.translate-x-32 { --transform-translate-x: 8rem; }
.translate-x-40 { --transform-translate-x: 10rem; }
.translate-x-48 { --transform-translate-x: 12rem; }
.translate-x-56 { --transform-translate-x: 14rem; }
.translate-x-64 { --transform-translate-x: 16rem; }
.translate-x-1\/2 { --transform-translate-x: 50%; }
.translate-x-1\/3 { --transform-translate-x: 33.333333%; }
.translate-x-2\/3 { --transform-translate-x: 66.666667%; }
.translate-x-1\/4 { --transform-translate-x: 25%; }
.translate-x-2\/4 { --transform-translate-x: 50%; }
.translate-x-3\/4 { --transform-translate-x: 75%; }
.translate-x-full { --transform-translate-x: 100%; }

.translate-y-0 { --transform-translate-y: 0px; }
.translate-y-1 { --transform-translate-y: 0.25rem; }
.translate-y-2 { --transform-translate-y: 0.5rem; }
.translate-y-3 { --transform-translate-y: 0.75rem; }
.translate-y-4 { --transform-translate-y: 1rem; }
.translate-y-5 { --transform-translate-y: 1.25rem; }
.translate-y-6 { --transform-translate-y: 1.5rem; }
.translate-y-8 { --transform-translate-y: 2rem; }
.translate-y-10 { --transform-translate-y: 2.5rem; }
.translate-y-12 { --transform-translate-y: 3rem; }
.translate-y-16 { --transform-translate-y: 4rem; }
.translate-y-20 { --transform-translate-y: 5rem; }
.translate-y-24 { --transform-translate-y: 6rem; }
.translate-y-32 { --transform-translate-y: 8rem; }
.translate-y-40 { --transform-translate-y: 10rem; }
.translate-y-48 { --transform-translate-y: 12rem; }
.translate-y-56 { --transform-translate-y: 14rem; }
.translate-y-64 { --transform-translate-y: 16rem; }
.translate-y-1\/2 { --transform-translate-y: 50%; }
.translate-y-1\/3 { --transform-translate-y: 33.333333%; }
.translate-y-2\/3 { --transform-translate-y: 66.666667%; }
.translate-y-1\/4 { --transform-translate-y: 25%; }
.translate-y-2\/4 { --transform-translate-y: 50%; }
.translate-y-3\/4 { --transform-translate-y: 75%; }
.translate-y-full { --transform-translate-y: 100%; }

/* Scale */
.scale-0 { --transform-scale-x: 0; --transform-scale-y: 0; }
.scale-50 { --transform-scale-x: 0.5; --transform-scale-y: 0.5; }
.scale-75 { --transform-scale-x: 0.75; --transform-scale-y: 0.75; }
.scale-90 { --transform-scale-x: 0.9; --transform-scale-y: 0.9; }
.scale-95 { --transform-scale-x: 0.95; --transform-scale-y: 0.95; }
.scale-100 { --transform-scale-x: 1; --transform-scale-y: 1; }
.scale-105 { --transform-scale-x: 1.05; --transform-scale-y: 1.05; }
.scale-110 { --transform-scale-x: 1.1; --transform-scale-y: 1.1; }
.scale-125 { --transform-scale-x: 1.25; --transform-scale-y: 1.25; }
.scale-150 { --transform-scale-x: 1.5; --transform-scale-y: 1.5; }

/* Rotate */
.rotate-0 { --transform-rotate: 0deg; }
.rotate-1 { --transform-rotate: 1deg; }
.rotate-2 { --transform-rotate: 2deg; }
.rotate-3 { --transform-rotate: 3deg; }
.rotate-6 { --transform-rotate: 6deg; }
.rotate-12 { --transform-rotate: 12deg; }
.rotate-45 { --transform-rotate: 45deg; }
.rotate-90 { --transform-rotate: 90deg; }
.rotate-180 { --transform-rotate: 180deg; }

/* ==========================================================================
   TRANSITION UTILITIES
========================================================================== */

.transition-none { transition: none; }
.transition-all { transition: all var(--duration-fast) var(--ease-out); }
.transition { transition: color var(--duration-fast) var(--ease-out), background-color var(--duration-fast) var(--ease-out), border-color var(--duration-fast) var(--ease-out), text-decoration-color var(--duration-fast) var(--ease-out), fill var(--duration-fast) var(--ease-out), stroke var(--duration-fast) var(--ease-out), opacity var(--duration-fast) var(--ease-out), box-shadow var(--duration-fast) var(--ease-out), transform var(--duration-fast) var(--ease-out), filter var(--duration-fast) var(--ease-out), backdrop-filter var(--duration-fast) var(--ease-out); }
.transition-colors { transition: color var(--duration-fast) var(--ease-out), background-color var(--duration-fast) var(--ease-out), border-color var(--duration-fast) var(--ease-out), text-decoration-color var(--duration-fast) var(--ease-out), fill var(--duration-fast) var(--ease-out), stroke var(--duration-fast) var(--ease-out); }
.transition-opacity { transition: opacity var(--duration-fast) var(--ease-out); }
.transition-shadow { transition: box-shadow var(--duration-fast) var(--ease-out); }
.transition-transform { transition: transform var(--duration-fast) var(--ease-out); }

/* Duration */
.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

/* Timing function */
.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* ==========================================================================
   ANIMATION UTILITIES
========================================================================== */

.animate-none { animation: none; }
.animate-spin { animation: spin 1s linear infinite; }
.animate-ping { animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-fade-in { animation: fadeIn var(--duration-normal) var(--ease-out); }
.animate-slide-up { animation: slideUp var(--duration-normal) var(--ease-out); }
.animate-slide-down { animation: slideDown var(--duration-normal) var(--ease-out); }
.animate-slide-left { animation: slideLeft var(--duration-normal) var(--ease-out); }
.animate-slide-right { animation: slideRight var(--duration-normal) var(--ease-out); }

/* ==========================================================================
   FINANCIAL UTILITIES
========================================================================== */

.price-positive {
  color: var(--positive);
  font-weight: var(--font-semibold);
}

.price-negative {
  color: var(--negative);
  font-weight: var(--font-semibold);
}

.price-neutral {
  color: var(--text-muted);
  font-weight: var(--font-medium);
}

.change-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  font-family: var(--font-mono);
  font-weight: var(--font-semibold);
}

.change-indicator.positive {
  color: var(--positive);
}

.change-indicator.negative {
  color: var(--negative);
}

.change-indicator.neutral {
  color: var(--text-muted);
}

.market-status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wider);
}

.market-status.open {
  background-color: var(--positive-alpha);
  color: var(--positive);
}

.market-status.closed {
  background-color: var(--negative-alpha);
  color: var(--negative);
}

.market-status.pre-market,
.market-status.after-hours {
  background-color: var(--warning-alpha);
  color: var(--warning);
}

/* ==========================================================================
   RESPONSIVE UTILITIES
========================================================================== */

/* Show/hide on different screen sizes */
@media (max-width: 767px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
}

@media (min-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
}

/* ==========================================================================
   ACCESSIBILITY UTILITIES
========================================================================== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.focus\:sr-only:focus {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus utilities */
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring:focus {
  box-shadow: var(--focus-ring);
}

.focus\:ring-accent:focus {
  box-shadow: 0 0 0 3px rgba(var(--accent-primary-rgb), 0.3);
}

.focus\:ring-positive:focus {
  box-shadow: 0 0 0 3px rgba(var(--positive-rgb), 0.3);
}

.focus\:ring-negative:focus {
  box-shadow: 0 0 0 3px rgba(var(--negative-rgb), 0.3);
}

/* ==========================================================================
   PRINT UTILITIES
========================================================================== */

@media print {
  .print\:hidden { display: none; }
  .print\:block { display: block; }
  .print\:flex { display: flex; }
  .print\:grid { display: grid; }
  .print\:text-black { color: black; }
  .print\:bg-white { background-color: white; }
  .print\:border-black { border-color: black; }
}

/* ==========================================================================
   DARK MODE UTILITIES
========================================================================== */

@media (prefers-color-scheme: dark) {
  .dark\:text-white { color: white; }
  .dark\:text-gray-100 { color: #f7fafc; }
  .dark\:text-gray-200 { color: #edf2f7; }
  .dark\:text-gray-300 { color: #e2e8f0; }
  .dark\:text-gray-400 { color: #cbd5e0; }
  .dark\:text-gray-500 { color: #a0aec0; }
  .dark\:bg-gray-800 { background-color: #2d3748; }
  .dark\:bg-gray-900 { background-color: #1a202c; }
  .dark\:border-gray-600 { border-color: #4a5568; }
  .dark\:border-gray-700 { border-color: #2d3748; }
}

/* ==========================================================================
   REDUCED MOTION UTILITIES
========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .motion-reduce\:animate-none { animation: none; }
  .motion-reduce\:transition-none { transition: none; }
  .motion-reduce\:transform-none { transform: none; }
}

/* ==========================================================================
   HIGH CONTRAST UTILITIES
========================================================================== */

@media (prefers-contrast: high) {
  .contrast-more\:border-black { border-color: black; }
  .contrast-more\:text-black { color: black; }
  .contrast-more\:bg-white { background-color: white; }
}

/* ==========================================================================
   CUSTOM UTILITY COMBINATIONS
========================================================================== */

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-x {
  display: flex;
  justify-content: center;
}

.center-y {
  display: flex;
  align-items: center;
}

.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.space-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.full-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.glass {
  background: rgba(var(--bg-secondary-rgb), 0.8);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(var(--border-primary-rgb), 0.5);
}

.gradient-text {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--border-primary) var(--bg-tertiary);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-full);
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--border-accent);
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}