import logging
import numpy as np
import pandas as pd
from typing import Optional, Any
from data_models.elite_impact_results import EliteImpactResultsV2_5
from data_models.elite_config_models import EliteConfig
from data_models.elite_intelligence import MarketRegime, ConvexValueColumns, EliteImpactColumns, FlowType, VolatilityRegime

logger = logging.getLogger(__name__)

class EliteImpactCalculator:
    """
    Consolidated elite impact calculator combining institutional intelligence
    and advanced flow analysis
    """
    
    def __init__(self, config: Optional[EliteConfig] = None):
        """Initialize with configuration"""
        self.config = config or EliteConfig()
        self.cv_cols = ConvexValueColumns()
        self.elite_cols = EliteImpactColumns()
        
    def calculate_elite_impact_score(
        self,
        options_df: pd.DataFrame,
        underlying_data: Any
    ) -> EliteImpactResultsV2_5:
        """
        Calculate comprehensive elite impact score
        Returns only canonical metrics as defined in data_models/elite_impact_results.py
        """
        try:
            if options_df.empty:
                raise ValueError("CRITICAL: Cannot calculate elite impact metrics - options_df is empty!")

            # Calculate components
            institutional_score = self._calculate_institutional_flow_score(options_df)
            flow_momentum = self._calculate_flow_momentum_index(options_df)
            market_regime = self._detect_market_regime(underlying_data)
            flow_type = self._classify_flow_type(options_df)
            volatility_regime = self._detect_volatility_regime(underlying_data)
            confidence = self._calculate_confidence(options_df, underlying_data)
            transition_risk = self._calculate_transition_risk(underlying_data)

            # Calculate overall elite impact score
            elite_impact_score = self._calculate_overall_elite_score(
                institutional_score, flow_momentum, confidence
            )

            return EliteImpactResultsV2_5(
                elite_impact_score_und=elite_impact_score,
                institutional_flow_score_und=institutional_score,
                flow_momentum_index_und=flow_momentum,
                market_regime_elite=market_regime.value,
                flow_type_elite=flow_type.value,
                volatility_regime_elite=volatility_regime.value,
                confidence=confidence,
                transition_risk=transition_risk
            )
        except Exception as e:
            logger.error(f"CRITICAL: Failed to calculate elite impact metrics: {e}")
            raise ValueError(f"CRITICAL: Failed to calculate elite impact metrics: {e}") from e
    
    def _calculate_institutional_flow_score(self, options_df: pd.DataFrame) -> float:
        """Calculate institutional flow score based on trade characteristics"""
        if options_df.empty:
            raise ValueError("CRITICAL: Cannot calculate institutional flow score - options_df is empty!")
        try:
            if self.cv_cols.VOLUME in options_df.columns:
                total_volume = options_df[self.cv_cols.VOLUME].sum()
                if total_volume <= 0:
                    raise ValueError("CRITICAL: Total volume is zero or negative - cannot calculate institutional flow score!")
                # Calculate score from real data only (example: scaled volume)
                return min(100.0, max(0.0, total_volume / 10000.0))
            else:
                raise ValueError("CRITICAL: VOLUME column missing in options_df - cannot calculate institutional flow score!")
        except Exception as e:
            logger.error(f"Error calculating institutional flow score: {e}")
            raise
    
    def _calculate_flow_momentum_index(self, options_df: pd.DataFrame) -> float:
        """Calculate flow momentum index"""
        if options_df.empty:
            return 0.0
        
        try:
            # Calculate directional flow
            if self.cv_cols.VOLUME_BS in options_df.columns:
                buy_volume = options_df[options_df[self.cv_cols.VOLUME_BS] > 0][
                    self.cv_cols.VOLUME
                ].sum()
                sell_volume = options_df[options_df[self.cv_cols.VOLUME_BS] < 0][
                    self.cv_cols.VOLUME
                ].sum()
                
                total_volume = buy_volume + sell_volume
                if total_volume > 0:
                    momentum = (buy_volume - sell_volume) / total_volume
                    return momentum
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating flow momentum: {e}")
            return 0.0
    
    def _detect_market_regime(self, underlying_data: Any) -> MarketRegime:
        """Detect current market regime"""
        try:
            # Extract volatility and trend data
            if hasattr(underlying_data, 'u_volatility'):
                volatility = underlying_data.u_volatility
            else:
                volatility = 0.2  # Default
            
            if hasattr(underlying_data, 'trend_strength'):
                trend_strength = underlying_data.trend_strength
            else:
                trend_strength = 0.5  # Default
            
            # Classify regime
            if volatility < 0.15:
                if trend_strength > 0.7:
                    return MarketRegime.LOW_VOL_TRENDING
                else:
                    return MarketRegime.LOW_VOL_RANGING
            elif volatility < 0.25:
                if trend_strength > 0.7:
                    return MarketRegime.MEDIUM_VOL_TRENDING
                else:
                    return MarketRegime.MEDIUM_VOL_RANGING
            elif volatility < 0.40:
                if trend_strength > 0.7:
                    return MarketRegime.HIGH_VOL_TRENDING
                else:
                    return MarketRegime.HIGH_VOL_RANGING
            else:
                return MarketRegime.STRESS_REGIME
                
        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return MarketRegime.REGIME_UNCLEAR_OR_TRANSITIONING
    
    def _classify_flow_type(self, options_df: pd.DataFrame) -> FlowType:
        """Classify the type of flow"""
        if options_df.empty:
            raise ValueError("CRITICAL: Cannot classify flow type - options_df is empty!")
        try:
            if self.cv_cols.VOLUME in options_df.columns:
                avg_trade_size = options_df[self.cv_cols.VOLUME].mean()
                # Use real data only; define thresholds inline or raise if not possible
                if avg_trade_size > 1000:
                    return FlowType.INSTITUTIONAL_DIRECTIONAL
                elif avg_trade_size > 100:
                    return FlowType.MIXED_FLOW
                else:
                    return FlowType.RETAIL_UNSOPHISTICATED
            else:
                raise ValueError("CRITICAL: VOLUME column missing in options_df - cannot classify flow type!")
        except Exception as e:
            logger.error(f"Error classifying flow type: {e}")
            raise
    
    def _detect_volatility_regime(self, underlying_data: Any) -> VolatilityRegime:
        """Detect volatility regime"""
        try:
            if hasattr(underlying_data, 'u_volatility') and underlying_data.u_volatility is not None:
                vol = underlying_data.u_volatility
                if vol < 0.15:
                    return VolatilityRegime.SUBDUED
                elif vol < 0.25:
                    return VolatilityRegime.NORMAL
                elif vol < 0.40:
                    return VolatilityRegime.ELEVATED
                else:
                    return VolatilityRegime.EXTREME
            else:
                raise ValueError("CRITICAL: u_volatility missing in underlying_data - cannot detect volatility regime!")
        except Exception as e:
            logger.error(f"Error detecting volatility regime: {e}")
            raise
    
    def _calculate_confidence(self, options_df: pd.DataFrame, underlying_data: Any) -> float:
        """Calculate confidence in the analysis"""
        try:
            confidence_factors = []
            if not options_df.empty:
                data_completeness = 1 - options_df.isnull().sum().sum() / (
                    len(options_df) * len(options_df.columns)
                )
                confidence_factors.append(data_completeness)
            if self.cv_cols.VOLUME in options_df.columns:
                total_volume = options_df[self.cv_cols.VOLUME].sum()
                volume_confidence = min(total_volume / 10000, 1.0)
                confidence_factors.append(volume_confidence)
            if confidence_factors:
                return float(np.mean(confidence_factors))
            else:
                return 0.5
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5
    
    def _calculate_transition_risk(self, underlying_data: Any) -> float:
        """Calculate risk of regime transition"""
        try:
            # Simple implementation based on volatility
            if hasattr(underlying_data, 'u_volatility'):
                vol = underlying_data.u_volatility
                # Higher volatility = higher transition risk
                return min(vol * 2, 1.0)
            
            return 0.5
            
        except Exception as e:
            logger.error(f"Error calculating transition risk: {e}")
            return 0.5
    
    def _calculate_overall_elite_score(
        self,
        institutional_score: float,
        flow_momentum: float,
        confidence: float
    ) -> float:
        """Calculate overall elite impact score"""
        try:
            # Weighted combination
            base_score = (
                institutional_score * 0.6 +
                abs(flow_momentum) * 100 * 0.4
            )
            
            # Apply confidence adjustment
            return base_score * (0.5 + confidence * 0.5)
            
        except Exception as e:
            logger.error(f"Error calculating elite score: {e}")
            return 50.0
    
    def _is_hedging_flow(self, options_df: pd.DataFrame) -> bool:
        """Check if flow appears to be hedging"""
        try:
            if self.cv_cols.OPTION_TYPE in options_df.columns:
                # Check for balanced put/call activity
                call_volume = options_df[
                    options_df[self.cv_cols.OPTION_TYPE] == 'call'
                ][self.cv_cols.VOLUME].sum()
                
                put_volume = options_df[
                    options_df[self.cv_cols.OPTION_TYPE] == 'put'
                ][self.cv_cols.VOLUME].sum()
                
                total_volume = call_volume + put_volume
                if total_volume > 0:
                    ratio = min(call_volume, put_volume) / max(call_volume, put_volume)
                    return ratio > 0.7  # Relatively balanced
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking hedging flow: {e}")
            return False
    
    def _get_default_results(self) -> EliteImpactResultsV2_5:
        """Get default results when calculation fails"""
        return EliteImpactResultsV2_5(
            elite_impact_score_und=50.0,
            institutional_flow_score_und=50.0,
            flow_momentum_index_und=0.0,
            market_regime_elite=MarketRegime.REGIME_UNCLEAR_OR_TRANSITIONING.value,
            flow_type_elite=FlowType.FLOW_UNCLEAR.value,
            volatility_regime_elite=VolatilityRegime.NORMAL.value,
            confidence=0.5,
            transition_risk=0.5
        )

__all__ = ['EliteImpactCalculator']
