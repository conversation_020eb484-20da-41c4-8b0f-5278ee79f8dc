#!/usr/bin/env python
"""
Quick debug - test imports one by one
"""

import sys
import os
import traceback

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_import(module_name, description):
    """Test a single import"""
    try:
        print(f"Testing: {description}")
        if module_name == "dotenv":
            from dotenv import load_dotenv
            print("✅ dotenv imported")
        elif module_name == "elite_root":
            import elite_options_system_v2_5
            print("✅ elite root package imported")
        elif module_name == "config_manager":
            from utils.config_manager_v2_5 import ConfigManagerV2_5
            print("✅ config manager imported")
        elif module_name == "config_init":
            from utils.config_manager_v2_5 import ConfigManagerV2_5
            config = ConfigManagerV2_5()
            print("✅ config manager initialized")
        elif module_name == "elite_config":
            from data_models.elite_config_models import EliteConfig
            print("✅ elite config imported")
        elif module_name == "db_manager":
            from data_management.database_manager_v2_5 import DatabaseManagerV2_5
            print("✅ database manager imported")
        elif module_name == "cache_manager":
            from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5
            print("✅ cache manager imported")
        elif module_name == "orchestrator":
            from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
            print("✅ orchestrator imported")
        elif module_name == "dashboard":
            from dashboard_application.app_main import main
            print("✅ dashboard main imported")
        else:
            exec(f"import {module_name}")
            print(f"✅ {module_name} imported")
            
    except Exception as e:
        print(f"❌ Failed to import {description}: {type(e).__name__}: {str(e)}")
        print(f"Traceback:\n{traceback.format_exc()}")
        return False
    return True

def main():
    """Test imports step by step"""
    tests = [
        ("dotenv", "Environment loader"),
        ("elite_root", "Elite root package"),
        ("config_manager", "Config manager class"),
        ("config_init", "Config manager initialization"),
        ("elite_config", "Elite config models"),
        ("db_manager", "Database manager"),
        ("cache_manager", "Cache manager"),
        ("orchestrator", "ITS Orchestrator"),
        ("dashboard", "Dashboard main")
    ]
    
    for module, desc in tests:
        if not test_import(module, desc):
            print(f"🛑 Stopping at failed import: {desc}")
            break
        print()

if __name__ == "__main__":
    main()
