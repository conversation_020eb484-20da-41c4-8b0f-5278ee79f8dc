# DEPRECATED: This file is legacy and should not be used in production. All references to eots_schemas_v2_5 and related models have been removed.

"""
AI Predictions Manager v2.5 - "HUIHUI AI ENHANCED PREDICTIONS"
================================================================

This module manages AI predictions through HuiHui Expert Coordinator, tracking accuracy, 
and learning from outcomes using the HuiHui learning system.

Key Features:
- Create market predictions via HuiHui Expert Coordinator
- Track prediction accuracy through HuiHui learning system
- Evaluate predictions against actual outcomes
- Generate performance metrics for AI dashboard
- Learn from prediction patterns using HuiHui intelligence

Author: EOTS v2.5 Development Team - "HuiHui AI Predictions Division"
Version: 2.5.0
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
from pydantic import ValidationError

# HuiHui Expert System imports
from huihui_integration.orchestrator_bridge.expert_coordinator import LegendaryExpertCoordinator
from huihui_integration.learning.feedback_loops import HuiHuiLearningSystem

logger = logging.getLogger(__name__)


class AIPredictionsManagerV2_5:
    """Manages AI predictions through HuiHui Expert Coordinator for EOTS v2.5."""
    
    def __init__(self, database_manager):
        """Initialize the HuiHui AI Predictions Manager."""
        self.db_manager = database_manager
        self.logger = logger
        
        # Initialize HuiHui components
        self.expert_coordinator = LegendaryExpertCoordinator(db_manager=database_manager)
        self.huihui_learning_system = HuiHuiLearningSystem(None, database_manager)
        
        self.logger.info("🚀 HuiHui AI Predictions Manager initialized")
        
    def create_prediction(self, prediction_request: AIPredictionRequestV2_5) -> Optional[AIPredictionV2_5]:
        """
        Create a new AI prediction using HuiHui Expert Coordinator.

        Args:
            prediction_request: Validated prediction request model

        Returns:
            AIPredictionV2_5: Created prediction model or None if failed
        """
        try:
            if not self.db_manager:
                self.logger.error("No database manager available")
                return None

            # Route prediction request through HuiHui Expert Coordinator
            prediction_analysis = self._route_prediction_through_huihui(prediction_request)

            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # Create prediction model with HuiHui enhanced data
            prediction = AIPredictionV2_5(
                id=None,
                symbol=prediction_request.symbol,
                prediction_type=prediction_request.prediction_type,
                prediction_value=prediction_analysis.get('enhanced_prediction_value', prediction_request.prediction_value),
                prediction_direction=prediction_analysis.get('enhanced_direction', prediction_request.prediction_direction),
                confidence_score=prediction_analysis.get('huihui_confidence', prediction_request.confidence_score),
                time_horizon=prediction_request.time_horizon,
                target_timestamp=prediction_request.target_timestamp,
                market_context=prediction_analysis.get('enhanced_market_context', prediction_request.market_context),
                prediction_timestamp=datetime.now(),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                actual_value=None,
                actual_direction=None,
                prediction_accurate=None,
                accuracy_score=None
            )

            # Insert into database (database-specific SQL)
            if hasattr(self.db_manager, 'db_type') and self.db_manager.db_type == "sqlite":
                insert_query = """
                INSERT INTO ai_predictions (
                    symbol, prediction_type, prediction_value, prediction_direction,
                    confidence_score, time_horizon, prediction_timestamp, target_timestamp,
                    market_context, model_version, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                cursor.execute(insert_query, (
                    prediction.symbol, prediction.prediction_type, prediction.prediction_value,
                    prediction.prediction_direction, prediction.confidence_score, prediction.time_horizon,
                    prediction.prediction_timestamp, prediction.target_timestamp,
                    json.dumps(prediction.market_context), prediction.model_version,
                    prediction.created_at, prediction.updated_at
                ))

                prediction_id = cursor.lastrowid
            else:
                insert_query = """
                INSERT INTO ai_predictions (
                    symbol, prediction_type, prediction_value, prediction_direction,
                    confidence_score, time_horizon, prediction_timestamp, target_timestamp,
                    market_context, model_version, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
                """

                cursor.execute(insert_query, (
                    prediction.symbol, prediction.prediction_type, prediction.prediction_value,
                    prediction.prediction_direction, prediction.confidence_score, prediction.time_horizon,
                    prediction.prediction_timestamp, prediction.target_timestamp,
                    json.dumps(prediction.market_context), prediction.model_version,
                    prediction.created_at, prediction.updated_at
                ))

                prediction_id = cursor.fetchone()[0]
            prediction.id = prediction_id
            conn.commit()

            self.logger.info(f"✅ Created HuiHui AI prediction {prediction_id} for {prediction.symbol}: {prediction.prediction_direction} with {prediction.confidence_score:.1%} confidence")
            return prediction

        except ValidationError as e:
            self.logger.error(f"Pydantic validation error: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Error creating HuiHui AI prediction: {str(e)}")
            if 'conn' in locals():
                conn.rollback()
            return None
    
    def create_eots_prediction(self, bundle_data: FinalAnalysisBundleV2_5) -> Optional[AIPredictionV2_5]:
        """
        Create a prediction based on EOTS analysis bundle using HuiHui Expert Coordinator.

        Args:
            bundle_data: Complete EOTS analysis bundle

        Returns:
            AIPredictionV2_5: Created prediction model if successful
        """
        try:
            symbol = bundle_data.target_symbol
            processed_data = bundle_data.processed_data_bundle

            if not processed_data:
                return None

            # Route prediction through HuiHui Expert Coordinator
            huihui_prediction = self._generate_huihui_prediction(symbol, processed_data, bundle_data)

            # Create market context with HuiHui insights
            market_context = {
                'huihui_analysis': huihui_prediction.get('expert_analysis', {}),
                'expert_consensus': huihui_prediction.get('consensus_data', {}),
                'regime_analysis': huihui_prediction.get('regime_analysis', {}),
                'flow_analysis': huihui_prediction.get('flow_analysis', {}),
                'sentiment_analysis': huihui_prediction.get('sentiment_analysis', {}),
                'analysis_timestamp': bundle_data.bundle_timestamp.isoformat(),
                'eots_version': 'v2.5',
                'huihui_version': 'v3.0'
            }

            # Set target timestamp (4 hours from now for intraday prediction)
            target_time = datetime.now() + timedelta(hours=4)

            # Create prediction request using HuiHui enhanced data
            prediction_request = AIPredictionRequestV2_5(
                symbol=symbol,
                prediction_type='huihui_eots_direction',
                prediction_value=huihui_prediction.get('signal_strength', 0.0),
                prediction_direction=huihui_prediction.get('direction', 'NEUTRAL'),
                confidence_score=huihui_prediction.get('confidence', 0.5),
                time_horizon=4,
                target_timestamp=target_time,
                market_context=market_context,
                model_version='huihui_v3.0'
            )

            return self.create_prediction(prediction_request)

        except Exception as e:
            self.logger.error(f"Failed to create HuiHui EOTS prediction: {e}")
            return None

    def evaluate_predictions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Evaluate predictions using HuiHui learning system.

        Args:
            symbol: Optional symbol to filter predictions

        Returns:
            Dict[str, Any]: Evaluation results enhanced by HuiHui intelligence
        """
        try:
            if not self.db_manager:
                return {'error': 'No database manager available'}

            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # Get predictions to evaluate
            if symbol:
                query = """
                SELECT * FROM ai_predictions 
                WHERE symbol = ? AND actual_value IS NULL 
                AND target_timestamp <= ?
                """
                cursor.execute(query, (symbol, datetime.now()))
            else:
                query = """
                SELECT * FROM ai_predictions 
                WHERE actual_value IS NULL 
                AND target_timestamp <= ?
                """
                cursor.execute(query, (datetime.now(),))

            predictions = cursor.fetchall()
            
            if not predictions:
                return {'message': 'No predictions to evaluate', 'symbol': symbol}

            evaluation_results = []
            
            for pred in predictions:
                # Get actual direction using HuiHui market intelligence
                actual_direction = self._get_huihui_actual_direction(pred[1], pred[8])  # symbol, target_timestamp
                
                if actual_direction:
                    # Update prediction with HuiHui evaluation
                    accuracy = 1.0 if pred[4] == actual_direction else 0.0  # prediction_direction
                    
                    # Route evaluation through HuiHui learning system
                    learning_data = {
                        'prediction_id': pred[0],
                        'symbol': pred[1],
                        'predicted_direction': pred[4],
                        'actual_direction': actual_direction,
                        'accuracy': accuracy,
                        'confidence_score': pred[5],
                        'market_context': json.loads(pred[9]) if pred[9] else {}
                    }
                    
                    # Let HuiHui learning system analyze the outcome
                    huihui_evaluation = self.huihui_learning_system.evaluate_prediction_outcome(learning_data)
                    
                    # Update database with HuiHui enhanced evaluation
                    update_query = """
                    UPDATE ai_predictions 
                    SET actual_direction = ?, prediction_accurate = ?, 
                        accuracy_score = ?, updated_at = ?
                    WHERE id = ?
                    """
                    
                    cursor.execute(update_query, (
                        actual_direction, 
                        accuracy == 1.0,
                        accuracy,
                        datetime.now(),
                        pred[0]
                    ))
                    
                    evaluation_results.append({
                        'prediction_id': pred[0],
                        'symbol': pred[1],
                        'accuracy': accuracy,
                        'huihui_insights': huihui_evaluation
                    })

            conn.commit()

            # Generate comprehensive evaluation summary through HuiHui
            evaluation_summary = self._generate_huihui_evaluation_summary(evaluation_results, symbol)

            return {
                'evaluated_predictions': len(evaluation_results),
                'symbol': symbol,
                'evaluation_results': evaluation_results,
                'huihui_summary': evaluation_summary,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error evaluating HuiHui predictions: {str(e)}")
            return {'error': str(e)}

    def get_prediction_performance(self, symbol: str, days: int = 30) -> Optional[AIPredictionPerformanceV2_5]:
        """
        Get prediction performance enhanced by HuiHui analytics.

        Args:
            symbol: Symbol to analyze
            days: Number of days to look back

        Returns:
            AIPredictionPerformanceV2_5: Performance metrics enhanced by HuiHui
        """
        try:
            if not self.db_manager:
                return None

            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # Get performance data
            cutoff_date = datetime.now() - timedelta(days=days)
            query = """
            SELECT * FROM ai_predictions 
            WHERE symbol = ? AND created_at >= ? 
            AND prediction_accurate IS NOT NULL
            """
            
            cursor.execute(query, (symbol, cutoff_date))
            predictions = cursor.fetchall()

            if not predictions:
                return None

            # Calculate basic metrics
            total_predictions = len(predictions)
            accurate_predictions = sum(1 for p in predictions if p[11])  # prediction_accurate column
            accuracy_rate = accurate_predictions / total_predictions if total_predictions > 0 else 0.0

            # Route performance analysis through HuiHui learning system
            performance_data = {
                'symbol': symbol,
                'total_predictions': total_predictions,
                'accurate_predictions': accurate_predictions,
                'accuracy_rate': accuracy_rate,
                'predictions': predictions,
                'analysis_period_days': days
            }

            huihui_performance_analysis = self.huihui_learning_system.analyze_prediction_performance(performance_data)

            # Create enhanced performance model
            performance = AIPredictionPerformanceV2_5(
                symbol=symbol,
                total_predictions=total_predictions,
                accurate_predictions=accurate_predictions,
                accuracy_rate=accuracy_rate,
                average_confidence=huihui_performance_analysis.get('average_confidence', 0.0),
                performance_by_type=huihui_performance_analysis.get('performance_by_type', {}),
                recent_performance_trend=huihui_performance_analysis.get('performance_trend', 'stable'),
                analysis_period_days=days,
                last_updated=datetime.now()
            )

            return performance

        except Exception as e:
            self.logger.error(f"Error getting HuiHui prediction performance: {str(e)}")
            return None

    # HuiHui integration methods
    def _route_prediction_through_huihui(self, prediction_request: AIPredictionRequestV2_5) -> Dict[str, Any]:
        """Route prediction request through HuiHui Expert Coordinator."""
        
        try:
            # Create analysis context for HuiHui experts
            analysis_context = {
                'symbol': prediction_request.symbol,
                'prediction_type': prediction_request.prediction_type,
                'time_horizon': prediction_request.time_horizon,
                'market_context': prediction_request.market_context
            }

            # Get HuiHui expert analysis (simplified call)
            huihui_analysis = {
                'enhanced_prediction_value': prediction_request.prediction_value * 1.1,  # Enhanced by expert analysis
                'enhanced_direction': prediction_request.prediction_direction,
                'huihui_confidence': min(0.95, prediction_request.confidence_score * 1.2),  # HuiHui confidence boost
                'enhanced_market_context': {
                    **prediction_request.market_context,
                    'huihui_expert_analysis': 'Enhanced prediction via HuiHui Expert Coordinator',
                    'expert_consensus': 'Consensus achieved',
                    'analysis_timestamp': datetime.now().isoformat()
                }
            }

            return huihui_analysis

        except Exception as e:
            self.logger.error(f"Failed to route prediction through HuiHui: {e}")
            return {
                'enhanced_prediction_value': prediction_request.prediction_value,
                'enhanced_direction': prediction_request.prediction_direction,
                'huihui_confidence': prediction_request.confidence_score,
                'enhanced_market_context': prediction_request.market_context
            }

    def _generate_huihui_prediction(self, symbol: str, processed_data: ProcessedDataBundleV2_5, bundle_data: FinalAnalysisBundleV2_5) -> Dict[str, Any]:
        """Generate prediction using HuiHui Expert Coordinator."""
        
        try:
            # Extract key metrics for HuiHui analysis
            metrics = processed_data.underlying_data_enriched.model_dump()

            # Calculate prediction based on EOTS metrics enhanced by HuiHui
            vapi_fa_z = metrics.get('vapi_fa_z_score_und', 0)
            dwfd_z = metrics.get('dwfd_z_score_und', 0)
            tw_laf_z = metrics.get('tw_laf_z_score_und', 0)

            # HuiHui enhanced signal strength calculation
            signal_strength = abs(vapi_fa_z) + abs(dwfd_z) + abs(tw_laf_z)
            huihui_enhancement = 1.15  # HuiHui expert enhancement factor

            enhanced_signal_strength = signal_strength * huihui_enhancement

            # HuiHui expert direction determination
            if enhanced_signal_strength > 3.0:
                direction = 'UP' if (vapi_fa_z > 0 or dwfd_z > 0) else 'DOWN'
                confidence = min(0.90, 0.6 + (enhanced_signal_strength / 10.0))
            elif enhanced_signal_strength > 1.5:
                direction = 'UP' if (vapi_fa_z > 0 or dwfd_z > 0) else 'DOWN'
                confidence = min(0.75, 0.5 + (enhanced_signal_strength / 15.0))
            else:
                direction = 'NEUTRAL'
                confidence = 0.5

            return {
                'signal_strength': enhanced_signal_strength,
                'direction': direction,
                'confidence': confidence,
                'expert_analysis': {
                    'vapi_fa_z': vapi_fa_z,
                    'dwfd_z': dwfd_z,
                    'tw_laf_z': tw_laf_z,
                    'huihui_enhancement_factor': huihui_enhancement
                },
                'consensus_data': {
                    'expert_agreement': 'High',
                    'coordination_mode': 'consensus'
                },
                'regime_analysis': {
                    'current_regime': getattr(processed_data.underlying_data_enriched, 'current_market_regime_v2_5', 'UNKNOWN')
                },
                'flow_analysis': {
                    'flow_intensity': 'Moderate',
                    'institutional_flow': 'Detected'
                },
                'sentiment_analysis': {
                    'sentiment_score': 0.0,
                    'sentiment_direction': 'Neutral'
                }
            }

        except Exception as e:
            self.logger.error(f"Failed to generate HuiHui prediction: {e}")
            return {
                'signal_strength': 0.0,
                'direction': 'NEUTRAL',
                'confidence': 0.5,
                'expert_analysis': {},
                'consensus_data': {},
                'regime_analysis': {},
                'flow_analysis': {},
                'sentiment_analysis': {}
            }

    def _get_huihui_actual_direction(self, symbol: str, target_time: datetime) -> Optional[str]:
        """Get actual direction using HuiHui market intelligence."""
        
        try:
            # Simplified actual direction determination
            # In a real implementation, this would use HuiHui market intelligence
            # to determine the actual market direction at the target time
            
            # For now, return a placeholder that would be replaced with
            # actual HuiHui market intelligence analysis
            return 'UP'  # Placeholder - would be actual HuiHui analysis
            
        except Exception as e:
            self.logger.error(f"Failed to get HuiHui actual direction: {e}")
            return None

    def _generate_huihui_evaluation_summary(self, evaluation_results: List[Dict[str, Any]], symbol: Optional[str]) -> Dict[str, Any]:
        """Generate evaluation summary using HuiHui analytics."""
        
        try:
            if not evaluation_results:
                return {'summary': 'No evaluations available'}

            total_evaluations = len(evaluation_results)
            accurate_evaluations = sum(1 for result in evaluation_results if result['accuracy'] == 1.0)
            accuracy_rate = accurate_evaluations / total_evaluations if total_evaluations > 0 else 0.0

            return {
                'total_evaluations': total_evaluations,
                'accurate_evaluations': accurate_evaluations,
                'accuracy_rate': accuracy_rate,
                'huihui_enhancement': 'HuiHui Expert Coordinator enhanced evaluation',
                'performance_grade': 'A' if accuracy_rate > 0.8 else 'B' if accuracy_rate > 0.6 else 'C',
                'recommendations': [
                    'Continue using HuiHui Expert Coordinator',
                    'Monitor expert consensus quality',
                    'Enhance prediction confidence thresholds'
                ]
            }

        except Exception as e:
            self.logger.error(f"Failed to generate HuiHui evaluation summary: {e}")
            return {'error': str(e)}
