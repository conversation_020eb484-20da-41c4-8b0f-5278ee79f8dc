/* =============================================================================
   ELITE OPTIONS TRADING SYSTEM - CHART COMPONENTS
   Trading charts, data visualization, and financial chart styling
============================================================================= */

/* ==========================================================================
   BASE CHART CONTAINER
========================================================================== */

.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  overflow: hidden;
}

.chart-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.chart-canvas {
  width: 100% !important;
  height: 100% !important;
}

/* ==========================================================================
   CHART HEADER
========================================================================== */

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-secondary);
}

.chart-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.chart-subtitle {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: 0;
  margin-top: var(--space-xs);
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.chart-timeframe {
  display: flex;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: 2px;
}

.chart-timeframe-btn {
  padding: var(--space-xs) var(--space-sm);
  background: none;
  border: none;
  border-radius: calc(var(--radius-md) - 2px);
  color: var(--text-muted);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  white-space: nowrap;
}

.chart-timeframe-btn:hover {
  color: var(--text-primary);
}

.chart-timeframe-btn.active {
  background-color: var(--bg-primary);
  color: var(--accent-primary);
  box-shadow: var(--shadow-sm);
}

/* ==========================================================================
   PRICE CHART SPECIFIC
========================================================================== */

.price-chart {
  position: relative;
}

.price-display {
  display: flex;
  align-items: baseline;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.current-price {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  font-family: var(--font-mono);
  color: var(--text-primary);
  line-height: 1;
}

.price-change {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  font-family: var(--font-mono);
}

.price-change.positive {
  color: var(--positive);
}

.price-change.negative {
  color: var(--negative);
}

.price-change.neutral {
  color: var(--text-muted);
}

.price-change-icon {
  width: 16px;
  height: 16px;
}

.price-percentage {
  font-size: var(--text-sm);
  opacity: 0.8;
}

/* ==========================================================================
   CANDLESTICK CHART
========================================================================== */

.candlestick-chart {
  position: relative;
}

.candlestick {
  cursor: crosshair;
}

.candlestick-bullish {
  fill: var(--positive);
  stroke: var(--positive);
}

.candlestick-bearish {
  fill: var(--negative);
  stroke: var(--negative);
}

.candlestick-doji {
  fill: var(--text-muted);
  stroke: var(--text-muted);
}

/* Volume bars */
.volume-bar {
  opacity: 0.6;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.volume-bar:hover {
  opacity: 1;
}

.volume-bar.bullish {
  fill: var(--positive-alpha);
}

.volume-bar.bearish {
  fill: var(--negative-alpha);
}

/* ==========================================================================
   LINE CHART
========================================================================== */

.line-chart {
  position: relative;
}

.chart-line {
  fill: none;
  stroke: var(--accent-primary);
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chart-area {
  fill: url(#chartGradient);
  opacity: 0.1;
}

.chart-gradient {
  --gradient-start: var(--accent-primary);
  --gradient-end: transparent;
}

.chart-point {
  fill: var(--accent-primary);
  stroke: var(--bg-primary);
  stroke-width: 2;
  r: 4;
  opacity: 0;
  transition: all var(--duration-fast) var(--ease-out);
}

.chart-point:hover {
  opacity: 1;
  r: 6;
}

/* ==========================================================================
   BAR CHART
========================================================================== */

.bar-chart {
  position: relative;
}

.chart-bar {
  transition: all var(--duration-fast) var(--ease-out);
  cursor: pointer;
}

.chart-bar:hover {
  opacity: 0.8;
  transform: scaleY(1.05);
}

.chart-bar.positive {
  fill: var(--positive);
}

.chart-bar.negative {
  fill: var(--negative);
}

.chart-bar.neutral {
  fill: var(--text-muted);
}

/* ==========================================================================
   HEATMAP
========================================================================== */

.heatmap {
  display: grid;
  gap: var(--space-xs);
  padding: var(--space-md);
}

.heatmap-tile {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  overflow: hidden;
}

.heatmap-tile:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: var(--shadow-lg);
}

.heatmap-tile.gain-strong {
  background-color: var(--positive);
  color: white;
}

.heatmap-tile.gain-moderate {
  background-color: var(--positive-alpha);
  color: var(--positive);
}

.heatmap-tile.gain-weak {
  background-color: rgba(34, 197, 94, 0.2);
  color: var(--positive);
}

.heatmap-tile.loss-weak {
  background-color: rgba(239, 68, 68, 0.2);
  color: var(--negative);
}

.heatmap-tile.loss-moderate {
  background-color: var(--negative-alpha);
  color: var(--negative);
}

.heatmap-tile.loss-strong {
  background-color: var(--negative);
  color: white;
}

.heatmap-tile.neutral {
  background-color: var(--bg-tertiary);
  color: var(--text-muted);
}

.heatmap-symbol {
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-xs);
  text-align: center;
}

.heatmap-price {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  font-family: var(--font-mono);
}

.heatmap-change {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  margin-top: var(--space-xs);
}

/* ==========================================================================
   GAUGE CHART
========================================================================== */

.gauge-chart {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.gauge-container {
  position: relative;
  width: 200px;
  height: 100px;
  overflow: hidden;
}

.gauge-background {
  stroke: var(--bg-tertiary);
  stroke-width: 8;
  fill: none;
}

.gauge-progress {
  stroke: var(--accent-primary);
  stroke-width: 8;
  fill: none;
  stroke-linecap: round;
  transition: stroke-dasharray var(--duration-normal) var(--ease-out);
}

.gauge-progress.positive {
  stroke: var(--positive);
}

.gauge-progress.negative {
  stroke: var(--negative);
}

.gauge-progress.warning {
  stroke: var(--warning);
}

.gauge-needle {
  stroke: var(--text-primary);
  stroke-width: 2;
  stroke-linecap: round;
  transform-origin: center;
  transition: transform var(--duration-normal) var(--ease-out);
}

.gauge-center {
  fill: var(--text-primary);
  r: 4;
}

.gauge-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  font-family: var(--font-mono);
  color: var(--text-primary);
  margin-top: var(--space-md);
}

.gauge-label {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin-top: var(--space-xs);
}

/* ==========================================================================
   DONUT/PIE CHART
========================================================================== */

.donut-chart {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-xl);
}

.donut-container {
  position: relative;
  flex-shrink: 0;
}

.donut-segment {
  transition: all var(--duration-fast) var(--ease-out);
  cursor: pointer;
}

.donut-segment:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.donut-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.donut-center-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  font-family: var(--font-mono);
  color: var(--text-primary);
  line-height: 1;
}

.donut-center-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  margin-top: var(--space-xs);
}

.donut-legend {
  flex: 1;
}

.donut-legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-sm);
  cursor: pointer;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.donut-legend-item:hover {
  opacity: 0.8;
}

.donut-legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.donut-legend-label {
  font-size: var(--text-sm);
  color: var(--text-primary);
  flex: 1;
}

.donut-legend-value {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  font-family: var(--font-mono);
  color: var(--text-muted);
}

/* ==========================================================================
   CHART TOOLTIPS
========================================================================== */

.chart-tooltip {
  position: absolute;
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-xs);
  color: var(--text-primary);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-tooltip);
  pointer-events: none;
  opacity: 0;
  transform: translateY(-8px);
  transition: all var(--duration-fast) var(--ease-out);
  backdrop-filter: var(--backdrop-blur);
  max-width: 200px;
}

.chart-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

.chart-tooltip-title {
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-xs);
  color: var(--text-primary);
}

.chart-tooltip-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-sm);
  margin-bottom: var(--space-xs);
}

.chart-tooltip-item:last-child {
  margin-bottom: 0;
}

.chart-tooltip-label {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--text-muted);
}

.chart-tooltip-color {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.chart-tooltip-value {
  font-weight: var(--font-semibold);
  font-family: var(--font-mono);
  color: var(--text-primary);
}

/* ==========================================================================
   CHART CROSSHAIR
========================================================================== */

.chart-crosshair {
  position: absolute;
  pointer-events: none;
  z-index: var(--z-overlay);
}

.chart-crosshair-line {
  stroke: var(--border-accent);
  stroke-width: 1;
  stroke-dasharray: 4 4;
  opacity: 0.7;
}

.chart-crosshair-label {
  fill: var(--bg-elevated);
  stroke: var(--border-primary);
  stroke-width: 1;
}

.chart-crosshair-text {
  fill: var(--text-primary);
  font-size: 10px;
  font-family: var(--font-mono);
  text-anchor: middle;
  dominant-baseline: central;
}

/* ==========================================================================
   CHART AXES
========================================================================== */

.chart-axis {
  stroke: var(--border-secondary);
  stroke-width: 1;
}

.chart-axis-tick {
  stroke: var(--border-secondary);
  stroke-width: 1;
}

.chart-axis-label {
  fill: var(--text-muted);
  font-size: var(--text-xs);
  font-family: var(--font-mono);
}

.chart-grid-line {
  stroke: var(--border-secondary);
  stroke-width: 0.5;
  opacity: 0.5;
  stroke-dasharray: 2 2;
}

.chart-grid-line.major {
  opacity: 0.8;
  stroke-dasharray: none;
}

/* ==========================================================================
   CHART LEGENDS
========================================================================== */

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
  margin-top: var(--space-lg);
  padding-top: var(--space-md);
  border-top: 1px solid var(--border-secondary);
}

.chart-legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  cursor: pointer;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.chart-legend-item:hover {
  opacity: 0.8;
}

.chart-legend-item.disabled {
  opacity: 0.5;
}

.chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.chart-legend-label {
  font-size: var(--text-sm);
  color: var(--text-primary);
}

/* ==========================================================================
   CHART LOADING STATES
========================================================================== */

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--bg-secondary-rgb), 0.8);
  backdrop-filter: var(--backdrop-blur);
  z-index: var(--z-overlay);
}

.chart-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--accent-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
  text-align: center;
}

.chart-empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: var(--space-md);
  opacity: 0.5;
}

.chart-empty-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-sm);
}

.chart-empty-description {
  font-size: var(--text-sm);
  max-width: 300px;
}

/* ==========================================================================
   CHART ZOOM CONTROLS
========================================================================== */

.chart-zoom {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  z-index: var(--z-overlay);
}

.chart-zoom-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  backdrop-filter: var(--backdrop-blur);
}

.chart-zoom-btn:hover {
  background-color: var(--bg-hover);
  border-color: var(--accent-primary);
}

.chart-zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chart-zoom-icon {
  width: 16px;
  height: 16px;
}

/* ==========================================================================
   MINI CHARTS
========================================================================== */

.mini-chart {
  height: 60px;
  position: relative;
}

.mini-chart .chart-line {
  stroke-width: 1.5;
}

.mini-chart .chart-area {
  opacity: 0.05;
}

.sparkline {
  height: 40px;
  width: 100%;
}

.sparkline .chart-line {
  stroke-width: 1;
}

/* ==========================================================================
   RESPONSIVE CHARTS
========================================================================== */

@media (max-width: 768px) {
  .chart-container {
    padding: var(--space-md);
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }
  
  .chart-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .chart-timeframe {
    flex: 1;
  }
  
  .price-display {
    flex-direction: column;
    gap: var(--space-sm);
  }
  
  .current-price {
    font-size: var(--text-2xl);
  }
  
  .donut-chart {
    flex-direction: column;
    gap: var(--space-lg);
  }
  
  .chart-legend {
    justify-content: center;
  }
  
  .heatmap {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  }
  
  .chart-zoom {
    position: static;
    flex-direction: row;
    justify-content: center;
    margin-top: var(--space-md);
  }
}

@media (max-width: 480px) {
  .chart-wrapper {
    min-height: 250px;
  }
  
  .chart-timeframe-btn {
    padding: var(--space-xs);
    font-size: 10px;
  }
  
  .heatmap {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  }
  
  .heatmap-tile {
    padding: var(--space-xs);
  }
  
  .gauge-container {
    width: 150px;
    height: 75px;
  }
}

/* ==========================================================================
   CHART ANIMATIONS
========================================================================== */

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-animate-in {
  animation: chartFadeIn var(--duration-normal) var(--ease-out);
}

/* ==========================================================================
   ACCESSIBILITY
========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .chart-line,
  .chart-bar,
  .gauge-progress,
  .gauge-needle,
  .donut-segment {
    transition: none;
  }
  
  .chart-loading-spinner {
    animation: none;
  }
}

.chart-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ==========================================================================
   CHART UTILITIES
========================================================================== */

.chart-full-height {
  height: 100%;
}

.chart-aspect-square {
  aspect-ratio: 1;
}

.chart-aspect-video {
  aspect-ratio: 16/9;
}

.chart-aspect-golden {
  aspect-ratio: 1.618;
}

.chart-no-data {
  opacity: 0.5;
  pointer-events: none;
}

.chart-interactive {
  cursor: crosshair;
}

.chart-static {
  pointer-events: none;
}