import logging
from enum import Enum
from dataclasses import dataclass
from typing import Dict
from pydantic import BaseModel, Field, ConfigDict
from data_models.elite_config_models import EliteConfig

logger = logging.getLogger(__name__)

# =============================================================================
# CONSOLIDATED DEFINITIONS - Enums and Dataclasses
# =============================================================================

class MarketRegime(Enum):
    """Market regime classifications for dynamic adaptation"""
    LOW_VOL_TRENDING = "low_vol_trending"
    LOW_VOL_RANGING = "low_vol_ranging"
    MEDIUM_VOL_TRENDING = "medium_vol_trending"
    MEDIUM_VOL_RANGING = "medium_vol_ranging"
    HIGH_VOL_TRENDING = "high_vol_trending"
    HIGH_VOL_RANGING = "high_vol_ranging"
    STRESS_REGIME = "stress_regime"
    EXPIRATION_REGIME = "expiration_regime"
    REGIME_UNCLEAR_OR_TRANSITIONING = "regime_unclear_or_transitioning"

class FlowType(Enum):
    """Flow classification types for institutional intelligence"""
    RETAIL_UNSOPHISTICATED = "retail_unsophisticated"
    RETAIL_SOPHISTICATED = "retail_sophisticated"
    INSTITUTIONAL_HEDGING = "institutional_hedging"
    INSTITUTIONAL_DIRECTIONAL = "institutional_directional"
    MARKET_MAKER_FLOW = "market_maker_flow"
    MIXED_FLOW = "mixed_flow"
    FLOW_UNCLEAR = "flow_unclear"

class VolatilityRegime(Enum):
    """Volatility regime classifications"""
    SUBDUED = "subdued"
    NORMAL = "normal"
    ELEVATED = "elevated"
    EXTREME = "extreme"

@dataclass
class ConvexValueColumns:
    """Column mappings for ConvexValue data"""
    # Price and Greeks
    STRIKE: str = 'strike'
    OPTION_TYPE: str = 'opt_kind'
    DELTA: str = 'delta_contract'
    GAMMA: str = 'gamma_contract'
    VEGA: str = 'vega_contract'
    THETA: str = 'theta_contract'
    
    # Volume and OI
    VOLUME: str = 'volm'
    OPEN_INTEREST: str = 'open_interest'
    
    # Exposure metrics
    DXOI: str = 'dxoi'
    GXOI: str = 'gxoi'
    VXOI: str = 'vxoi'
    TXOI: str = 'txoi'
    
    # Flow metrics
    VOLUME_BS: str = 'volm_bs'
    VALUE_BS: str = 'value_bs'
    
    # Additional metrics
    IMPLIED_VOL: str = 'implied_volatility'
    DTE: str = 'dte_calc'

@dataclass
class EliteImpactColumns:
    """Column mappings for elite impact calculations"""
    # Elite scores
    ELITE_IMPACT_SCORE: str = 'elite_impact_score_und'
    INSTITUTIONAL_FLOW_SCORE: str = 'institutional_flow_score_und'
    FLOW_MOMENTUM_INDEX: str = 'flow_momentum_index_und'
    
    # Regime classifications
    MARKET_REGIME: str = 'market_regime_elite'
    FLOW_TYPE: str = 'flow_type_elite'
    VOLATILITY_REGIME: str = 'volatility_regime_elite'
    
    # Confidence metrics
    CONFIDENCE: str = 'confidence'
    TRANSITION_RISK: str = 'transition_risk'

__all__ = [
    'EliteConfig',
    'MarketRegime', 'FlowType', 'VolatilityRegime',
    'ConvexValueColumns', 'EliteImpactColumns'
]
