from pydantic import BaseModel, Field, ConfigDict

class EliteImpactResultsV2_5(BaseModel):
    """
    STRICT PYDANTIC V2-ONLY: Elite impact calculation results model.
    Replaces dictionary returns with proper Pydantic v2 structure.
    """
    elite_impact_score_und: float = Field(..., description="Master composite elite impact score for the underlying.")
    institutional_flow_score_und: float = Field(..., description="Institutional flow score for the underlying.")
    flow_momentum_index_und: float = Field(..., description="Flow momentum index for the underlying.")
    market_regime_elite: str = Field(..., description="Elite classified market regime.")
    flow_type_elite: str = Field(..., description="Elite classified flow type.")
    volatility_regime_elite: str = Field(..., description="Elite classified volatility regime.")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence level for analysis.")
    transition_risk: float = Field(..., ge=0.0, le=1.0, description="Transition risk score.")

    model_config = ConfigDict(extra='forbid') 