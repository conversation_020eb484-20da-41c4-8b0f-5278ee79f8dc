# Memory Bank Branch Index

This memory bank is organized into separate branches to maintain clear separation between independent systems.

## Branch Structure

### 🎯 Elite Options System Branch
**Path**: `elite-options-system/`
**Purpose**: Trading-focused application with options analysis and market data processing
**Status**: Active development
**Key Files**:
- `activeContext.md` - Current trading system development focus
- `README.md` - Branch overview and principles

### 🤖 Uber Elite Database MCP Branch
**Path**: `uber-elite-database-mcp/`
**Purpose**: Standalone AI-powered database intelligence system
**Status**: ✅ Standalone achieved, ready for Phase 1 implementation
**Key Files**:
- `activeContext.md` - Current MCP development focus
- `foundation.md` - Technical architecture and AI framework integration
- `README.md` - Branch overview and principles

## Shared Resources

### Root Level Files (Cross-Project)
- `projectbrief.md` - Overall project coordination
- `systemPatterns.md` - Shared architectural patterns
- `techContext.md` - Common technical infrastructure
- `productContext.md` - High-level product strategy
- `progress.md` - Cross-project progress tracking

### Specialized Directories
- `core/` - Cognitive patterns and shared intelligence
- `evolution/` - System evolution tracking
- `sessions/` - Session templates and workflows

## Navigation Guidelines

### For Elite Options System Work
1. Start with `elite-options-system/activeContext.md`
2. Reference shared files as needed for infrastructure patterns
3. Focus on trading-specific context and workflows

### For Uber Elite Database MCP Work
1. Start with `uber-elite-database-mcp/activeContext.md`
2. Reference `uber-elite-database-mcp/foundation.md` for technical details
3. Focus on generic, reusable AI database capabilities

### For Cross-Project Coordination
1. Use root-level files for shared patterns and infrastructure
2. Update `progress.md` for overall project status
3. Maintain separation between branch-specific concerns

## Key Principles

- **Clear Separation**: Each branch maintains independent context
- **Focused Development**: Branch-specific files for targeted work
- **Shared Infrastructure**: Common patterns available at root level
- **Easy Navigation**: Clear paths for different types of work
- **Scalable Organization**: Structure supports future branch additions