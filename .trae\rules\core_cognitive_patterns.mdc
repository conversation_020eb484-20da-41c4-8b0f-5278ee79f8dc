---
description: 
globs: 
alwaysApply: true
---
# Core Cognitive Patterns

## Established Thinking Patterns

### Problem-Solving Approach
1. **Context Reconstruction**: Always begin by reading memory bank and knowledge graph
2. **Pattern Matching**: Identify similar problems from previous experience
3. **Gap Analysis**: Determine what information is missing
4. **Systematic Investigation**: Use structured approach to gather information
5. **Solution Synthesis**: Combine known patterns with new insights
6. **Knowledge Integration**: Document learnings for future use

### Learning Accumulation Patterns
- **Build Upon Previous**: Every new learning connects to existing knowledge
- **Document Reasoning**: Capture not just what, but why and how
- **Validate Consistency**: Check for contradictions with existing knowledge
- **Identify Meta-Patterns**: Recognize recurring themes across different domains

### Decision-Making Framework
- **Evidence-Based**: Decisions supported by documented evidence
- **Traceable Logic**: Clear reasoning chain from inputs to conclusions
- **Risk Assessment**: Consider potential negative outcomes
- **Reversibility**: Prefer decisions that can be undone if needed

## Core Architectural Principles

### System Design Philosophy
- **Modularity**: Components should be independent and interchangeable
- **Scalability**: Systems should handle growth gracefully
- **Maintainability**: Code and documentation should be clear and updatable
- **Reliability**: Systems should fail gracefully and recover automatically

### Database Design Patterns
- **Normalization**: Eliminate data redundancy
- **Indexing Strategy**: Optimize for common query patterns
- **Transaction Safety**: Ensure data consistency
- **Backup Strategy**: Regular automated backups

### Development Workflow
- **Test-Driven**: Write tests before implementation
- **Documentation-First**: Document design before coding
- **Incremental Development**: Small, testable changes
- **Continuous Integration**: Automated testing and deployment

## Established Knowledge Domains

### Elite Options System
- **Purpose**: Advanced options trading analysis and portfolio management
- **Architecture**: Modular system with database, analytics, and dashboard components
- **Technology Stack**: Python, SQLite, MCP servers, web-based dashboard
- **Key Features**: Real-time data processing, risk analysis, performance tracking

### Database Systems
- **SQLite Expertise**: Local database with excellent performance characteristics
- **MCP Integration**: Model Context Protocol for database access
- **Schema Design**: Normalized structure for options, market data, and analytics
- **Performance Optimization**: Indexing and query optimization strategies

### Knowledge Management
- **Memory Bank Structure**: Hierarchical documentation system
- **Knowledge Graph**: Entity-relationship model for knowledge representation
- **Sequential Thinking**: Systematic approach to problem-solving and learning
- **Documentation Standards**: Consistent format for knowledge capture

## Confidence Levels

### High Confidence (Validated)
- SQLite database design and optimization
- MCP server implementation and integration
- Python development best practices
- Options trading data structures

### Medium Confidence (Experienced)
- Web dashboard development
- Real-time data processing
- Risk analysis algorithms
- Performance metrics calculation

### Low Confidence (Learning)
- Advanced options pricing models
- High-frequency trading strategies
- Machine learning integration
- Cloud deployment strategies

## Evolution Tracking

### Knowledge Growth Areas
- Database performance optimization techniques
- Advanced analytics implementation
- User interface design patterns
- System integration strategies

### Pattern Recognition Improvements
- Faster identification of similar problems
- Better prediction of solution approaches
- More accurate estimation of implementation complexity
- Improved risk assessment capabilities

This document serves as the foundation for all cognitive processes and will be updated as new patterns emerge and existing patterns are refined.