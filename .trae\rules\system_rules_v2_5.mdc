---
description: # Elite Options System v2.5 - System Rules
alwaysApply: true
---


# *Elite Options System v2.5 - System Rules*

## MCP Server Hierarchy and Trae AI Desktop Integration

### MCP Server Architecture Overview

The Elite Options System v2.5 utilizes a hierarchical MCP (Model Context Protocol) server architecture integrated within the Trae AI desktop environment. **CRITICAL: All MCP servers are configured and managed through Trae AI desktop settings and accessed via `run_mcp` - NEVER create local implementations.**

**Trae AI Desktop Configuration**: All MCP tools are pre-configured within the Trae AI desktop application settings. No external configuration files (such as claude_desktop_config.json) are required or used in this environment.

The following diagram illustrates the optimized server hierarchy with all 8 MCP tools:

```mermaid
graph TD
    User["User Interface"] --> TM["TaskManager MCP"]
    User --> ST["Sequential Thinking MCP"]
    User --> PP["Puppeteer MCP"]
    
    TM --> PK["Persistent Knowledge Graph MCP"]
    ST --> PK
    PP --> PK
    
    PK --> Memory["Memory MCP"]
    PK --> Exa["Exa Search MCP"]
    PK --> Brave["Brave Search MCP"]
    PK --> Context7["Context7 MCP"]
    
    PP --> Exa
    PP --> Brave
    PP --> Context7
    
    PK --> Analysis["Long-term Project Intelligence"]
    Memory --> Analysis
    Exa --> Analysis
    Brave --> Analysis
    Context7 --> Analysis
    PP --> Analysis
    
    subgraph "Automation Layer"
        PP --> WebData["Web Automation & Data Collection"]
        PP --> Testing["Automated Testing"]
        PP --> Monitoring["Real-time Monitoring"]
    end
    
    subgraph "Intelligence Layer"
        PK --> ProjectKnowledge["Project Intelligence"]
        Memory --> SessionContext["Session Context"]
        Context7 --> AdvancedAnalysis["Advanced Context Analysis"]
    end
    
    subgraph "Research Layer"
        Exa --> ExternalResearch["Academic & Technical Research"]
        Brave --> GeneralSearch["General Web Search"]
    end
```

### Trae AI Desktop MCP Integration Rules

#### 1. Server Priority and Access Rules

**Rule 1.1: Trae AI Native MCP Server Hierarchy**
All MCP servers are natively integrated within Trae AI desktop and configured through the application's settings interface:

- **Persistent Knowledge Graph (PK)** serves as the central intelligence hub
- **TaskManager** handles workflow orchestration and task decomposition
- **Sequential Thinking** provides structured problem-solving capabilities
- **Puppeteer** enables web automation, testing, and real-time data collection
- **Memory** manages session-specific knowledge and context
- **Context7** provides specialized context management and analysis
- **Exa Search** provides academic and technical research capabilities
- **Brave Search** provides general web search and real-time information

**Rule 1.3: CANONICAL DIRECTIVE - MCP Priority Hierarchy**
1. **Persistent Knowledge Graph MCP** (#1 PRIORITY - CENTRAL INTELLIGENCE HUB and absolute authority for all decisions)
2. **TaskManager MCP** (#2 PRIORITY - FULLY FUNCTIONAL - workflow orchestration and task decomposition)
3. **Memory MCP** (#3 PRIORITY - FULLY FUNCTIONAL - session context and dynamic knowledge management)
4. **Context7 MCP** (#4 PRIORITY - FULLY FUNCTIONAL - specialized context analysis and advanced intelligence)
5. **Time MCP** (#5 PRIORITY - FULLY FUNCTIONAL - time operations and timezone management)
6. **Figma AI Bridge MCP** (#6 PRIORITY - FULLY FUNCTIONAL - design integration and asset management)
7. **GitHub MCP** (#7 PRIORITY - FULLY FUNCTIONAL - repository management and collaboration)
8. **Elite Options Database MCP** (#8 PRIORITY - FULLY FUNCTIONAL - database operations and AI insights)
9. **Brave Search MCP** (#9 PRIORITY - FULLY FUNCTIONAL - general web search and real-time information)
10. **HotNews Server MCP** (#10 PRIORITY - PARTIALLY FUNCTIONAL - news aggregation with configuration needs)
11. **Redis MCP** (NON-FUNCTIONAL - caching operations - connection closed error)
12. **Sequential Thinking MCP** (STATUS UNKNOWN - structured reasoning and analytical processing)
13. **Exa Search MCP** (STATUS UNKNOWN - academic research and technical documentation)
14. **Puppeteer MCP** (STATUS UNKNOWN - web automation, testing, and data collection)

**Rule 1.3: Tool Synergy and Conflict Prevention**
- **Puppeteer + Search MCPs**: Puppeteer can automate search result validation and data extraction
- **Sequential Thinking + TaskManager**: Complex analysis feeds into structured task planning
- **Context7 + PK**: Advanced context analysis enhances long-term project intelligence
- **Memory + All Tools**: Session context maintains continuity across all tool interactions
- **Conflict Prevention**: Never run multiple web automation tools simultaneously; coordinate search operations to avoid redundancy

#### 2. Data Flow and Integration Rules

**Rule 2.1: Knowledge Persistence and Data Flow**
- All significant project insights MUST be stored in the Persistent Knowledge Graph
- Session-specific context goes to Memory MCP
- Task workflows are managed through TaskManager MCP
- Web automation results from Puppeteer feed into PK for long-term intelligence
- Real-time data collection is processed and stored appropriately

**Rule 2.2: Cross-Server Communication Patterns**
- Sequential Thinking outputs feed into Persistent Knowledge Graph
- TaskManager task completions update Persistent Knowledge Graph
- Puppeteer automation results enhance search operations and data validation
- Search results (Exa/Brave) are processed and stored in appropriate knowledge stores
- Context7 analysis enriches all data flows with advanced contextual intelligence

**Rule 2.3: Data Consistency and Integration**
- Persistent Knowledge Graph serves as the source of truth for project intelligence
- Memory MCP maintains session consistency across all tool interactions
- Puppeteer provides real-time validation of web-based information
- All servers must maintain data integrity across operations
- Cross-tool validation prevents data conflicts and ensures accuracy

#### 3. Usage Guidelines and Best Practices

**Rule 3.1: Comprehensive Server Selection Criteria**
- Use **TaskManager** for: Multi-step workflows, project planning, task decomposition, approval workflows
- Use **Sequential Thinking** for: Complex problem analysis, architectural decisions, debugging, step-by-step reasoning
- Use **Persistent Knowledge Graph** for: Long-term project knowledge, entity relationships, system intelligence, project history
- Use **Puppeteer** for: FUNCTIONAL - Web automation, UI testing, data scraping, real-time monitoring, screenshot capture, form automation (5/7 tools working, 2 with workarounds)
- Use **Memory** for: Session context, temporary knowledge, dynamic information, conversation continuity
- Use **Context7** for: Specialized context management, advanced analysis, contextual intelligence, pattern recognition
- Use **Brave Search** for: PRIMARY SEARCH - General web search, real-time information, news, current events, broad research, academic content
- Use **Exa Search** for: STANDBY ONLY - Academic research, technical documentation (currently non-functional due to configuration issues)
- Use **Elite Options Database** for: FULLY FUNCTIONAL - Database operations, AI-enhanced analytics, trading data management (all 10 tools operational with 15 database tables)

**Rule 3.2: Optimal Tool Combination Patterns**
- **Research Workflow**: Brave Search (PRIMARY) → Puppeteer (FUNCTIONAL - validation & automation) → PK (storage) [Exa Search on standby]
- **Testing Workflow**: TaskManager (planning) → Puppeteer (FUNCTIONAL - execution & screenshots) → Memory (results) → PK (insights)
- **Analysis Workflow**: Sequential Thinking → Context7 → PK (synthesis) → Memory (session tracking)
- **Data Collection**: Puppeteer (FUNCTIONAL - automation & extraction) → Brave Search (validation) → PK (long-term storage)
- **Monitoring Workflow**: Puppeteer (FUNCTIONAL - real-time monitoring) → Context7 (analysis) → PK (intelligence storage)
- **Fallback Pattern**: If Brave Search fails → Use built-in web_search → Avoid Exa Search until configuration resolved

**Rule 3.3: Conflict Resolution Hierarchy**
- In case of conflicting information between servers:
  1. **Persistent Knowledge Graph** takes ABSOLUTE precedence as the primary source of truth
  2. **Sequential Thinking** provides structured conflict analysis and reasoning
  3. **Puppeteer** takes precedence for real-time web data validation
  4. **Memory MCP** takes precedence for session-specific context
  5. **Context7** provides advanced analytical resolution
  6. **Search MCPs** provide external validation and verification
- ALL decisions must be validated against Knowledge Graph before implementation

**Rule 3.4: Performance Optimization and Resource Management**
- Minimize redundant calls across servers
- Cache frequently accessed information in appropriate stores
- Use batch operations when possible for Puppeteer automation
- Prioritize local knowledge over external searches
- Coordinate web automation to prevent resource conflicts
- Implement intelligent caching between Search MCPs and Puppeteer
- Use Memory MCP to track recent operations and avoid duplicates

#### 4. Security and Access Control

**Rule 4.1: Data Security**
- No sensitive information (API keys, passwords) stored in any MCP server
- All data flows must maintain project confidentiality
- External search results are filtered for relevance and safety

**Rule 4.2: Access Control**
- All MCP servers operate within defined project scope
- No unauthorized external system access
- Maintain audit trail of significant operations

#### 5. Maintenance and Monitoring

**Rule 5.1: Health Monitoring**
- Regular verification of server connectivity and responsiveness
- Monitor data consistency across servers
- Track performance metrics and optimization opportunities

**Rule 5.2: Knowledge Management**
- Regular cleanup of obsolete session data in Memory MCP
- Periodic consolidation of insights in Persistent Knowledge Graph
- Maintain clear entity relationships and observations

#### 6. Error Handling and Fallbacks

**Rule 6.1: CANONICAL DIRECTIVE - Server Failure Handling**
- If **Persistent Knowledge Graph** (#1) is unavailable, SYSTEM ENTERS EMERGENCY MODE with immediate restoration protocols
- **Sequential Thinking** (#2) provides temporary analytical support but CANNOT make final decisions without Knowledge Graph
- **Memory MCP** (#3) maintains session continuity during Knowledge Graph restoration
- **TaskManager** (#4) suspends major workflow decisions until Knowledge Graph is restored
- **Memory Bank** (#5) provides read-only access to historical project intelligence
- ALL operations must prioritize Knowledge Graph restoration as ABSOLUTE HIGHEST PRIORITY
- NO MAJOR DECISIONS can be made without Knowledge Graph validation and approval

**Rule 6.2: Data Recovery**
- Maintain backup strategies for critical knowledge
- Implement graceful degradation when servers are unavailable
- Ensure system functionality with minimal server set

### Implementation Guidelines

#### CANONICAL DIRECTIVE - Trae AI Desktop MCP Integration
**All MCP servers are pre-configured within Trae AI desktop settings and automatically available through the `run_mcp` interface.**

#### Server Initialization Sequence (Trae AI Native)
1. **Persistent Knowledge Graph MCP** (#1 PRIORITY - ABSOLUTE CENTRAL AUTHORITY and intelligence hub)
2. **TaskManager MCP** (#2 PRIORITY - FUNCTIONAL - workflow orchestration)
3. **Memory MCP** (#3 PRIORITY - FUNCTIONAL - session context and dynamic knowledge)
4. **Context7 MCP** (#4 PRIORITY - FUNCTIONAL - advanced context analysis)
5. **Time MCP** (#5 PRIORITY - FUNCTIONAL - time operations and timezone management)
6. **Figma AI Bridge MCP** (#6 PRIORITY - FUNCTIONAL - design integration)
7. **GitHub MCP** (#7 PRIORITY - FUNCTIONAL - repository management)
8. **HotNews Server MCP** (#8 PRIORITY - PARTIALLY FUNCTIONAL - news aggregation)
9. **Brave Search MCP** (STATUS UNKNOWN - general web search and real-time information)
10. **Exa Search MCP** (STATUS UNKNOWN - academic research)
11. Verify cross-server connectivity through Trae AI desktop interface
12. Load existing project knowledge and establish baselines from Knowledge Graph
13. Configure tool synergy patterns with Knowledge Graph as central hub
14. Establish Knowledge Graph as primary decision-making authority

**Note**: No external configuration files or manual server setup required - all managed through Trae AI desktop application.

#### Comprehensive Daily Operations Workflow (Trae AI Desktop)
1. **Trae AI System Health Check**
   - Verify all 10 MCP server connectivity through Trae AI desktop interface (7 functional, 1 partially functional, 3 non-functional/unknown)
   - Check available MCP tools readiness within Trae AI environment
   - Validate search API availability and rate limits through Trae AI settings

2. **Context Loading and Preparation**
   - Load relevant context from Persistent Knowledge Graph via `run_mcp`
   - Initialize Memory MCP with session context through Trae AI interface
   - Prepare Context7 for advanced analysis tasks using native integration

3. **Task Execution with Optimal Tool Selection**
   - Use TaskManager for complex workflow orchestration via `run_mcp`
   - Apply Sequential Thinking for analytical tasks through Trae AI integration
   - Deploy Puppeteer for web automation and data collection using native tools
   - Leverage Search MCPs for external research and validation

4. **Knowledge Integration and Storage**
   - Store significant insights in Persistent Knowledge Graph using `run_mcp`
   - Maintain session context in Memory MCP through Trae AI native interface
   - Cross-validate findings using multiple tools within Trae AI ecosystem
   - Update Context7 with enriched contextual intelligence

5. **Performance Monitoring and Optimization**
   - Track tool usage patterns through Trae AI desktop analytics
   - Monitor resource consumption via Trae AI system monitoring
   - Optimize tool combination workflows within native environment
   - Maintain audit trail through Trae AI logging system

### Compliance and Governance (Trae AI Desktop)

**Rule 7.1: Documentation Requirements**
- All significant MCP server interactions through `run_mcp` must be documented
- Maintain clear audit trail of knowledge evolution within Trae AI environment
- Document MCP tool usage patterns and configurations within Trae AI settings

**Rule 7.2: Quality Assurance**
- Regular validation of stored knowledge accuracy through Trae AI native tools
- Verification of MCP server performance through Trae AI desktop monitoring
- Continuous improvement of tool utilization patterns within Trae AI ecosystem

### Elite Options System v2.5 Specific MCP Integration Patterns

#### Puppeteer MCP for Options Trading Analytics

**Rule 8.1: Financial Data Collection and Validation**
- Use Puppeteer for real-time options flow monitoring from web sources
- Automate data validation against multiple financial data providers
- Capture screenshots of trading interfaces for audit trails
- Monitor competitor platforms for market intelligence

**Rule 8.2: Automated Testing for Trading Systems**
- Implement end-to-end testing of dashboard functionality
- Automate user interface testing for options analytics displays
- Validate real-time data updates and alert systems
- Test responsive design across multiple screen sizes

**Rule 8.3: Integration with Search MCPs for Market Research**
- **Brave Search + Context7**: PRIMARY - Real-time news monitoring, sentiment analysis, and pattern recognition
- **Puppeteer + Brave Search**: FUNCTIONAL - Real-time monitoring, automated data collection, and validation
- **Puppeteer + Context7**: FUNCTIONAL - Advanced pattern recognition with automated web data extraction
- **Puppeteer + Exa Search**: PUPPETEER FUNCTIONAL, EXA STANDBY - Academic research validation (Puppeteer ready, avoid Exa Search until configuration resolved)

#### Advanced Workflow Patterns for Elite Options System

**Pattern 1: Comprehensive Market Analysis Workflow**
```
TaskManager (plan analysis) → 
Sequential Thinking (strategy development) → 
Brave Search (PRIMARY - market news & academic content) → 
Puppeteer (FUNCTIONAL - automated data validation & extraction) → 
Context7 (pattern analysis & validation) → 
PK (store insights) → 
Memory (session tracking)
[Exa Search bypassed - on standby until configuration resolved]
```

**Pattern 2: Automated Testing and Quality Assurance**
```
TaskManager (test planning) → 
Sequential Thinking (test strategy development) → 
Puppeteer (FUNCTIONAL - UI automation, screenshots, interaction testing) → 
Memory (test results tracking) → 
Context7 (quality analysis) → 
PK (quality metrics storage)
[Note: 5/7 Puppeteer tools functional, use workarounds for form filling and select operations]
```

**Pattern 3: Real-time Information Monitoring**
```
Puppeteer (FUNCTIONAL - continuous web monitoring & data extraction) → 
Brave Search (PRIMARY - information validation & supplementation) → 
Context7 (anomaly detection & pattern analysis) → 
Sequential Thinking (alert analysis) → 
PK (alert history) → 
Memory (session alerts)
[Built-in web_search as fallback if Brave Search fails]
[Note: Puppeteer provides automated monitoring capabilities with screenshot documentation]
```

#### Security and Compliance for Financial Data

**Rule 8.4: Financial Data Security (Puppeteer Functional)**
- All Puppeteer automation must comply with financial data regulations
- Implement secure credential management for trading platform access
- Maintain audit trails of all automated data collection activities
- Use Puppeteer screenshot capabilities for visual audit documentation
- Ensure compliance with market data licensing agreements
- Implement proper error handling for the 2 Puppeteer tools with known issues

**Rule 8.5: Performance Standards for Trading Systems**
- Puppeteer operations must not impact real-time trading performance
- Implement circuit breakers for automated data collection
- Monitor and optimize resource usage during market hours
- Maintain sub-second response times for critical operations

These comprehensive rules ensure optimal utilization of all 8 MCP servers while maintaining system integrity, performance, and regulatory compliance for the Elite Options System v2.5 project.

## Project Overview
The Elite Options System v2.5 is a comprehensive options trading analytics platform that provides real-time data processing, advanced flow analysis, and intelligent dashboard visualization for options market participants.

## Core Architecture Principles

### 1. Data Pipeline Architecture
- **Real-time Processing**: All market data must be processed with sub-second latency
- **Fault Tolerance**: System must gracefully handle data source failures
- **Scalability**: Architecture must support increasing data volumes
- **Data Integrity**: All data transformations must be auditable and reversible

### 2. Component Structure
```
Elite Options System v2.5/
├── core_analytics_engine/     # Core processing logic
├── dashboard_application/     # Web-based UI
├── data_management/          # Data ingestion and storage
├── data_models/             # Pydantic models and schemas
├── config/                  # Configuration management
└── utils/                   # Shared utilities
```

### 3. Technology Stack Standards
- **Backend**: Python 3.11+ with FastAPI
- **Frontend**: Streamlit for dashboard interface
- **Data Processing**: Pandas, NumPy for analytics
- **Configuration**: Pydantic for type-safe config management
- **Logging**: Structured logging with JSON format
- **Testing**: Pytest with comprehensive coverage

## Development Guidelines

### 1. Code Quality Standards
- **Type Hints**: All functions must include proper type annotations
- **Documentation**: Docstrings required for all public methods
- **Error Handling**: Comprehensive exception handling with meaningful messages
- **Code Style**: Follow PEP 8 with line length of 88 characters

### 2. Configuration Management
- All configuration must use Pydantic models
- Environment-specific configs in separate files
- No hardcoded values in business logic
- Configuration validation at startup

### 3. Data Models
- Use Pydantic BaseModel for all data structures
- Implement proper validation rules
- Include field descriptions and examples
- Version all model schemas

### 4. Error Handling Patterns
```python
# Standard error handling pattern
try:
    result = risky_operation()
    logger.info(f"Operation completed successfully: {result}")
except SpecificException as e:
    logger.error(f"Specific error occurred: {e}")
    raise CustomException(f"Failed to complete operation: {e}") from e
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise
```

## Feature Implementation Rules

### 1. Advanced Flow Analysis
- Must process all option flow data in real-time
- Implement configurable filtering and alerting
- Support multiple analysis timeframes
- Provide exportable results

### 2. Dashboard Requirements
- Responsive design for multiple screen sizes
- Real-time data updates without page refresh
- Configurable layouts and widgets
- Export capabilities for all visualizations

### 3. Data Management
- Implement proper data lifecycle management
- Support for historical data archiving
- Efficient caching strategies
- Data backup and recovery procedures

## Security Guidelines

### 1. API Security
- All endpoints must implement proper authentication
- Rate limiting on all public endpoints
- Input validation and sanitization
- Secure error messages (no sensitive data exposure)

### 2. Data Protection
- Encrypt sensitive configuration data
- Implement proper access controls
- Log all data access and modifications
- Regular security audits

## Testing Standards

### 1. Test Coverage
- Minimum 80% code coverage required
- Unit tests for all business logic
- Integration tests for API endpoints
- End-to-end tests for critical workflows

### 2. Test Structure
```python
# Standard test structure
def test_feature_name_scenario():
    # Arrange
    setup_data = create_test_data()
    
    # Act
    result = function_under_test(setup_data)
    
    # Assert
    assert result.status == "success"
    assert result.data is not None
```

## Performance Requirements

### 1. Response Times
- API endpoints: < 200ms for 95th percentile
- Dashboard loading: < 3 seconds initial load
- Real-time updates: < 100ms latency
- Data processing: < 1 second for standard operations

### 2. Resource Usage
- Memory usage: < 2GB under normal load
- CPU usage: < 70% average utilization
- Disk I/O: Efficient caching to minimize reads
- Network: Optimize payload sizes

## Deployment Guidelines

### 1. Environment Management
- Separate configurations for dev/staging/prod
- Use environment variables for sensitive data
- Implement proper logging levels per environment
- Health checks for all services

### 2. Monitoring and Alerting
- Application performance monitoring
- Error rate tracking and alerting
- Resource utilization monitoring
- Business metric tracking

## Documentation Requirements

### 1. Code Documentation
- README files for each major component
- API documentation with examples
- Configuration guide with all options
- Troubleshooting guides

### 2. Architecture Documentation
- System architecture diagrams
- Data flow documentation
- Integration specifications
- Deployment procedures

## Version Control Standards

### 1. Commit Guidelines
- Use conventional commit format
- Include issue references where applicable
- Write descriptive commit messages
- Keep commits atomic and focused

### 2. Branch Strategy
- Feature branches for new development
- Release branches for version preparation
- Hotfix branches for critical fixes
- Main branch always deployable

## Maintenance Procedures

### 1. Regular Maintenance
- Weekly dependency updates
- Monthly security scans
- Quarterly performance reviews
- Annual architecture reviews

### 2. Incident Response
- Immediate notification procedures
- Escalation matrix for different severity levels
- Post-incident review process
- Documentation of lessons learned

## MCP Server Usage Guardrails (Trae AI Desktop)

### CRITICAL: Trae AI Native MCP Tools Only

**NEVER CREATE LOCAL MCP IMPLEMENTATIONS**

All MCP servers in this system are natively integrated within Trae AI desktop and must be accessed exclusively via the `run_mcp` tool. Creating local implementations is strictly prohibited and will cause system conflicts.

### Available Trae AI Native MCP Servers
Configured and managed through Trae AI desktop settings:

- `mcp.config.usrlocalmcp.time` - Time operations
- `mcp.config.usrlocalmcp.Figma AI Bridge` - Figma integration
- `mcp.config.usrlocalmcp.GitHub` - GitHub operations
- `mcp.config.usrlocalmcp.redis` - Redis database
- `mcp.config.usrlocalmcp.elite-options-database` - Elite options database
- `mcp.config.usrlocalmcp.HotNews Server` - News aggregation
- `mcp.config.usrlocalmcp.Puppeteer` - Web automation
- `mcp.config.usrlocalmcp.Persistent Knowledge Graph` - Knowledge management
- `mcp.config.usrlocalmcp.exa` - Advanced search
- `mcp.config.usrlocalmcp.Brave Search` - Web search
- `mcp.config.usrlocalmcp.TaskManager` - Task coordination
- `mcp.config.usrlocalmcp.Sequential Thinking` - Reasoning chains
- `mcp.config.usrlocalmcp.Memory` - Session memory
- `mcp.config.usrlocalmcp.context7` - Context analysis

### Context7 MCP Tool Parameter Specifications

**CRITICAL: Correct Parameter Names for Context7 Tools**

The Context7 MCP tools require specific parameter names that differ from the documentation examples:

#### resolve-library-id Tool
- **Correct Parameter**: `libraryName` (NOT `package_name`)
- **Usage Example**:
  ```json
  {
    "server_name": "mcp.config.usrlocalmcp.context7",
    "tool_name": "resolve-library-id",
    "args": {
      "libraryName": "pydantic-ai"
    }
  }
  ```

#### get-library-docs Tool
- **Correct Parameter**: `context7CompatibleLibraryID` (NOT `library_id`)
- **Usage Example**:
  ```json
  {
    "server_name": "mcp.config.usrlocalmcp.context7",
    "tool_name": "get-library-docs",
    "args": {
      "context7CompatibleLibraryID": "/pydantic/pydantic-ai"
    }
  }
  ```

**Parameter Validation Rules**:
1. Always use `libraryName` for library search operations
2. Always use `context7CompatibleLibraryID` for documentation retrieval
3. The library ID format typically follows `/organization/library-name` pattern
4. Verify parameter names match exactly as shown above to avoid MCP errors

### Mandatory Usage Rules (Trae AI Desktop)

1. **Access Method**: Always use `run_mcp` tool with proper server_name and tool_name through Trae AI interface
2. **No Local Files**: Never create .py files, configs, or implementations for MCP servers - all managed by Trae AI
3. **No Duplicates**: Never attempt to replicate MCP functionality locally - use Trae AI native integration
4. **Verification**: If an MCP server appears unavailable, check Trae AI desktop settings before assuming implementation needed
5. **Documentation**: Reference MCP capabilities but rely on Trae AI native configuration management
6. **Configuration**: All MCP server configuration is handled through Trae AI desktop settings - no external config files

### Violation Prevention (Trae AI Environment)

If you find yourself about to:
- Create a file ending in `_mcp.py`
- Write MCP server implementation code
- Create external MCP configuration files (like claude_desktop_config.json)
- Build local versions of Trae AI native MCP tools
- Manually configure MCP servers outside Trae AI settings

**STOP IMMEDIATELY** and use the existing Trae AI native MCP integration via `run_mcp` instead.

---

*This document should be reviewed and updated quarterly to ensure it remains current with project evolution and industry best practices.*