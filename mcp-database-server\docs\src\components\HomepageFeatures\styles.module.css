.features {
  display: flex;
  align-items: center;
  padding: 2rem 0;
  width: 100%;
  background-color: var(--ifm-color-emphasis-100);
}

.featureSvg {
  height: 140px;
  width: 140px;
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease;
}

.featureItem {
  padding: 2rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  height: 100%;
}

.featureItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.featureItem:hover .featureSvg {
  transform: scale(1.05);
}

.featureTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.featureDescription {
  font-size: 1rem;
  line-height: 1.5;
}
