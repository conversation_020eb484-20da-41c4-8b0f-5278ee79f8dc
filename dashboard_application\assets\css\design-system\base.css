/* =============================================================================
   ELITE OPTIONS TRADING SYSTEM - BASE STYLES
   Global foundations, typography, and element defaults
============================================================================= */

/* ==========================================================================
   GLOBAL RESET & FOUNDATIONS
========================================================================== */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  min-height: 100vh;
}

/* ==========================================================================
   TYPOGRAPHY HIERARCHY
========================================================================== */

h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
}

h2 {
  font-size: var(--text-2xl);
}

h3 {
  font-size: var(--text-xl);
}

h4 {
  font-size: var(--text-lg);
}

h5 {
  font-size: var(--text-base);
}

h6 {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
  color: var(--text-secondary);
}

p {
  margin-bottom: var(--space-md);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

p:last-child {
  margin-bottom: 0;
}

small {
  font-size: var(--text-xs);
  color: var(--text-muted);
}

strong, b {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

em, i {
  font-style: italic;
}

code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas',
    'Courier New', monospace;
  font-size: 0.875em;
  background-color: var(--bg-tertiary);
  color: var(--accent-primary);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
}

pre {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas',
    'Courier New', monospace;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  overflow-x: auto;
  line-height: var(--leading-normal);
}

pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
}

/* ==========================================================================
   LINKS & INTERACTIVE ELEMENTS
========================================================================== */

a {
  color: var(--accent-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-out);
}

a:hover {
  color: var(--accent-primary-hover);
  text-decoration: underline;
}

a:focus {
  outline: none;
  box-shadow: var(--focus-ring);
  border-radius: var(--radius-xs);
}

a:active {
  color: var(--accent-primary-dim);
}

/* ==========================================================================
   LISTS
========================================================================== */

ul, ol {
  margin-bottom: var(--space-md);
  padding-left: var(--space-lg);
}

li {
  margin-bottom: var(--space-xs);
  color: var(--text-secondary);
}

li:last-child {
  margin-bottom: 0;
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

/* Clean lists for navigation */
.list-none {
  list-style: none;
  padding-left: 0;
}

.list-none li {
  margin-bottom: 0;
}

/* ==========================================================================
   TABLES
========================================================================== */

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--space-lg);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-soft);
}

th {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  font-weight: var(--font-semibold);
  text-align: left;
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-primary);
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
}

td {
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-secondary);
  color: var(--text-secondary);
  vertical-align: top;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: var(--bg-hover);
}

/* ==========================================================================
   FORM ELEMENTS
========================================================================== */

input, textarea, select {
  font-family: inherit;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  transition: all var(--duration-fast) var(--ease-out);
  width: 100%;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: var(--focus-ring);
  background-color: var(--bg-tertiary);
}

input:hover,
textarea:hover,
select:hover {
  border-color: var(--border-accent);
  background-color: var(--bg-hover);
}

input:disabled,
textarea:disabled,
select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--bg-primary);
  color: var(--text-disabled);
}

input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

label {
  display: block;
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  font-size: var(--text-sm);
}

fieldset {
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
}

legend {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  padding: 0 var(--space-sm);
  font-size: var(--text-sm);
}

/* ==========================================================================
   BUTTONS - Base styles (extended in components)
========================================================================== */

button {
  font-family: inherit;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  cursor: pointer;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-lg);
  transition: all var(--duration-fast) var(--ease-out);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  text-decoration: none;
  user-select: none;
  position: relative;
  overflow: hidden;
}

button:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* ==========================================================================
   IMAGES & MEDIA
========================================================================== */

img {
  max-width: 100%;
  height: auto;
  display: block;
}

svg {
  display: block;
  max-width: 100%;
  height: auto;
}

figure {
  margin: 0 0 var(--space-lg) 0;
}

figcaption {
  font-size: var(--text-sm);
  color: var(--text-muted);
  text-align: center;
  margin-top: var(--space-sm);
  font-style: italic;
}

/* ==========================================================================
   HORIZONTAL RULES
========================================================================== */

hr {
  border: none;
  height: 1px;
  background-color: var(--border-primary);
  margin: var(--space-xl) 0;
}

/* ==========================================================================
   BLOCKQUOTES
========================================================================== */

blockquote {
  border-left: 4px solid var(--accent-primary);
  padding-left: var(--space-lg);
  margin: var(--space-lg) 0;
  font-style: italic;
  color: var(--text-accent);
  background-color: var(--bg-secondary);
  padding: var(--space-lg);
  border-radius: var(--radius-md);
}

blockquote p:last-child {
  margin-bottom: 0;
}

/* ==========================================================================
   SELECTION
========================================================================== */

::selection {
  background-color: var(--accent-primary-alpha);
  color: var(--text-primary);
}

::-moz-selection {
  background-color: var(--accent-primary-alpha);
  color: var(--text-primary);
}

/* ==========================================================================
   SCROLLBARS - Webkit
========================================================================== */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-accent);
  border-radius: var(--radius-full);
  transition: background var(--duration-fast) var(--ease-out);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

::-webkit-scrollbar-corner {
  background: var(--bg-primary);
}

/* ==========================================================================
   FOCUS MANAGEMENT
========================================================================== */

.focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* Hide focus ring for mouse users */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
  box-shadow: none;
}

/* ==========================================================================
   ACCESSIBILITY
========================================================================== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  z-index: var(--z-tooltip);
}

.skip-link:focus {
  top: 6px;
}

/* ==========================================================================
   REDUCED MOTION
========================================================================== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ==========================================================================
   HIGH CONTRAST MODE
========================================================================== */

@media (prefers-contrast: high) {
  button {
    border: 2px solid currentColor;
  }
  
  input, textarea, select {
    border-width: 2px;
  }
  
  a {
    text-decoration: underline;
  }
}

/* ==========================================================================
   PRINT STYLES
========================================================================== */

@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  
  thead {
    display: table-header-group;
  }
  
  tr,
  img {
    page-break-inside: avoid;
  }
  
  img {
    max-width: 100% !important;
  }
  
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}