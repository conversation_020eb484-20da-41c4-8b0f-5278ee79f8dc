# tests/test_ai_dashboard_models.py
"""
Comprehensive Unit Tests for AI Hub Pydantic Models (EOTS v2.5)
================================================================

PYDANTIC-FIRST & ZERO DICT ACCEPTANCE.

This test suite provides exhaustive validation for all Pydantic V2 models
defined in `data_models/ai_dashboard_models.py`. It ensures that every model
is robust, type-safe, and ready for production use in the AI Hub.

The tests cover:
1.  **Model Validation**: Correctness with valid and invalid data.
2.  **Constraint Enforcement**: `confloat`, `conint`, `Literal`, `Enum` checks.
3.  **Serialization**: `model_dump()` and `model_dump_json()` correctness.
4.  **Deserialization**: `model_validate()` and `model_validate_json()` correctness.
5.  **Model Composition**: Validation of deeply nested models (e.g., `AIHubStateModel`).
6.  **Edge Cases**: Optional fields, empty lists, boundary values.
7.  **Real-World Simulation**: Using fixtures to build realistic data structures.
8.  **Performance**: A marked test for validation performance on large datasets.
"""

import pytest
import json
from datetime import datetime
from typing import List, Dict, Any

from pydantic import ValidationError

# Import all models to be tested
from data_models.ai_dashboard_models import (
    PanelType, ComponentStatus, RecommendationStrength, CompassTheme,
    TypographyStyle, CardStyle, BadgeStyle,
    GaugeConfigModel, ChartLayoutConfig,
    PanelConfigModel, MetricDisplayModel, MetricsPanelModel,
    AIRecommendationItem, AIRecommendationsPanelModel,
    MarketCompassSegment, MarketCompassModel,
    ExpertStatusModel, HuiHuiExpertsMonitorModel,
    AIHubLayoutConfig, AIHubStateModel
)
# Import other required models for building test data
from data_models.ai_ml_models import MOEUnifiedResponseV2_5, MOEExpertResponseV2_5, MOEExpertResponseDataV2_5
from data_models.trading_market_models import MarketRegimeAnalysisDetails, OptionsFlowAnalysisDetails, SentimentAnalysisDetails

# --- Test Fixtures for Reusable, Realistic Data ---

@pytest.fixture
def valid_market_compass_segment_data() -> Dict[str, Any]:
    """Provides valid data for a MarketCompassSegment."""
    return {
        "label": "Market Regime",
        "score": 0.75,
        "color": "#00d4ff",
        "description": "Regime: Bullish Trend",
        "tactical_advice": "Favor long positions."
    }

@pytest.fixture
def valid_market_compass_model_data(valid_market_compass_segment_data) -> Dict[str, Any]:
    """Provides valid data for a MarketCompassModel."""
    return {
        "panel_config": {
            "id": "market-compass-spy",
            "title": "Legendary Market Compass",
            "panel_type": "market_compass"
        },
        "segments": [valid_market_compass_segment_data] * 4,
        "overall_directional_bias": 0.65,
        "bias_label": "Strong Bullish",
        "active_theme": "elite",
        "tactical_summary": "Overall bias is strongly bullish."
    }

@pytest.fixture
def valid_ai_recommendation_item_data() -> Dict[str, Any]:
    """Provides valid data for an AIRecommendationItem."""
    return {
        "id": "rec-123",
        "strategy_name": "Bullish Trend Continuation",
        "direction": "Bullish",
        "instrument_type": "Call",
        "strength": "High Conviction",
        "confidence_score": 0.88,
        "rationale": ["Strong regime alignment.", "High institutional flow."],
        "target_price": 550.0,
        "stop_loss": 540.0,
        "timeframe": "1-3 Days",
        "generated_at": datetime.now().isoformat()
    }

@pytest.fixture
def valid_ai_hub_state_data(valid_market_compass_model_data, valid_ai_recommendation_item_data) -> Dict[str, Any]:
    """Provides a deeply nested, valid data structure for the master AIHubStateModel."""
    return {
        "hub_status": "OK",
        "last_updated": datetime.now().isoformat(),
        "target_symbol": "SPY",
        "error_message": None,
        "layout_config": {
            "row1": ["market_compass", "trade_recommendations", "market_analysis"],
            "row2": ["flow_intelligence", "volatility_gamma", "custom_formulas"],
            "row3": ["data_pipeline_monitor", "huihui_experts_monitor", "performance_monitor", "alerts_status"]
        },
        "market_compass": valid_market_compass_model_data,
        "ai_recommendations": {
            "panel_config": {
                "id": "ai-recs-spy",
                "title": "AI-Driven Recommendations",
                "panel_type": "trade_recommendations"
            },
            "recommendations": [valid_ai_recommendation_item_data]
        },
        "flow_intelligence_metrics": {
            "panel_config": {"id": "flow-intel", "title": "Flow Intel", "panel_type": "flow_intelligence"},
            "metrics": [{
                "name": "VAPI-FA",
                "value": 85.5,
                "gauge_config": {"value": 85.5, "title": "VAPI-FA", "range_min": -100, "range_max": 100}
            }]
        },
        "volatility_gamma_metrics": None,
        "advanced_options_metrics": None,
        "huihui_monitor": {
            "panel_config": {"id": "huihui-mon", "title": "HuiHui Monitor", "panel_type": "huihui_experts_monitor"},
            "experts": [{
                "expert_id": "market_regime",
                "expert_name": "Market Regime Expert",
                "status": "OK",
                "last_response_time_ms": 55.3,
                "last_confidence": 0.92,
                "last_seen": datetime.now().isoformat()
            }]
        },
        "raw_analysis_bundle": None # Not testing this deep model here, assume None
    }


# --- Test Suite ---

class TestSimpleModels:
    """Tests for simple Enum and styling models."""

    def test_enums(self):
        """Ensures Enum values are correct."""
        assert PanelType.MARKET_COMPASS == "market_compass"
        assert ComponentStatus.OK == "OK"
        assert RecommendationStrength.HIGH == "High Conviction"
        assert CompassTheme.ELITE == "elite"

    def test_style_models_forbid_extra_fields(self):
        """Verifies that style models reject unknown fields."""
        with pytest.raises(ValidationError):
            TypographyStyle(fontSize="1rem", unknown_field="should-fail")
        with pytest.raises(ValidationError):
            CardStyle(backgroundColor="#fff", padding="10px", color="#000", borderRadius="5px", unknown_field="should-fail")
        with pytest.raises(ValidationError):
            BadgeStyle(backgroundColor="#fff", padding="5px", color="#000", borderRadius="3px", fontSize="10px", fontWeight="bold", unknown_field="should-fail")

    def test_style_models_instantiation(self):
        """Tests successful instantiation of style models."""
        style = TypographyStyle(fontSize="16px", fontWeight="700", color="#FFFFFF")
        assert style.fontSize == "16px"
        assert style.model_dump()['fontWeight'] == "700"


class TestChartAndPanelConfigModels:
    """Tests for chart and panel configuration models."""

    def test_gauge_config_model(self):
        """Tests GaugeConfigModel validation."""
        # Valid
        gauge = GaugeConfigModel(value=50, title="Test Gauge", range_min=0, range_max=100)
        assert gauge.value == 50
        # Invalid (no validation on value itself, but let's test types)
        with pytest.raises(ValidationError):
            GaugeConfigModel(value="invalid", title="Test Gauge", range_min=0, range_max=100)

    def test_panel_config_model(self):
        """Tests PanelConfigModel validation."""
        # Valid
        config = PanelConfigModel(id="test-panel", title="Test Panel", panel_type=PanelType.MARKET_ANALYSIS)
        assert config.panel_type == PanelType.MARKET_ANALYSIS
        # Invalid enum value
        with pytest.raises(ValidationError):
            PanelConfigModel(id="test-panel", title="Test Panel", panel_type="invalid_panel_type")


class TestNestedComponentModels:
    """Tests for models that contain other models."""

    def test_metrics_panel_model(self):
        """Tests the nesting of MetricDisplayModel within MetricsPanelModel."""
        data = {
            "panel_config": {"id": "p1", "title": "t1", "panel_type": "flow_intelligence"},
            "metrics": [
                {
                    "name": "Metric 1", "value": 1.5,
                    "gauge_config": {"value": 1.5, "title": "M1", "range_min": -2, "range_max": 2}
                }
            ]
        }
        panel = MetricsPanelModel.model_validate(data)
        assert len(panel.metrics) == 1
        assert isinstance(panel.metrics[0], MetricDisplayModel)
        assert panel.metrics[0].gauge_config.value == 1.5

        # Test with invalid nested data
        data["metrics"][0]["gauge_config"]["value"] = "not-a-float"
        with pytest.raises(ValidationError):
            MetricsPanelModel.model_validate(data)


class TestHighPriorityModels:
    """Tests for the user-prioritized Market Compass and AI Recommendations."""

    def test_market_compass_model_validation(self, valid_market_compass_model_data):
        """Tests successful validation and constraint checking for MarketCompassModel."""
        # Valid
        model = MarketCompassModel.model_validate(valid_market_compass_model_data)
        assert model.bias_label == "Strong Bullish"
        assert len(model.segments) == 4

        # Invalid: score out of bounds
        invalid_data = valid_market_compass_model_data.copy()
        invalid_data["segments"][0]["score"] = 1.1 # Should be between 0.0 and 1.0
        with pytest.raises(ValidationError):
            MarketCompassModel.model_validate(invalid_data)

        # Invalid: bias out of bounds
        invalid_data = valid_market_compass_model_data.copy()
        invalid_data["overall_directional_bias"] = -1.5 # Should be between -1.0 and 1.0
        with pytest.raises(ValidationError):
            MarketCompassModel.model_validate(invalid_data)

    def test_ai_recommendation_item_validation(self, valid_ai_recommendation_item_data):
        """Tests successful validation and constraint checking for AIRecommendationItem."""
        # Valid
        model = AIRecommendationItem.model_validate(valid_ai_recommendation_item_data)
        assert model.confidence_score == 0.88

        # Invalid: confidence score out of bounds
        invalid_data = valid_ai_recommendation_item_data.copy()
        invalid_data["confidence_score"] = -0.1
        with pytest.raises(ValidationError):
            AIRecommendationItem.model_validate(invalid_data)

        # Invalid: empty rationale list
        invalid_data = valid_ai_recommendation_item_data.copy()
        invalid_data["rationale"] = []
        with pytest.raises(ValidationError):
            AIRecommendationItem.model_validate(invalid_data)

        # Invalid: bad literal value for direction
        invalid_data = valid_ai_recommendation_item_data.copy()
        invalid_data["direction"] = "Sideways"
        with pytest.raises(ValidationError):
            AIRecommendationItem.model_validate(invalid_data)


class TestMasterStateModel:
    """Comprehensive tests for the top-level AIHubStateModel."""

    def test_ai_hub_state_successful_validation(self, valid_ai_hub_state_data):
        """Tests that a realistic, valid, and deeply nested payload is correctly validated."""
        hub_state = AIHubStateModel.model_validate(valid_ai_hub_state_data)

        assert hub_state.target_symbol == "SPY"
        assert hub_state.hub_status == ComponentStatus.OK
        assert isinstance(hub_state.market_compass, MarketCompassModel)
        assert isinstance(hub_state.ai_recommendations, AIRecommendationsPanelModel)
        assert isinstance(hub_state.huihui_monitor.experts[0], ExpertStatusModel)
        assert hub_state.market_compass.segments[0].score == 0.75
        assert hub_state.ai_recommendations.recommendations[0].confidence_score == 0.88

    def test_ai_hub_state_serialization_roundtrip(self, valid_ai_hub_state_data):
        """Ensures that dumping and re-validating the model results in an identical object."""
        original_model = AIHubStateModel.model_validate(valid_ai_hub_state_data)
        
        # Simulate storing in dcc.Store (dump to dict) and reading back
        dumped_data = original_model.model_dump()
        reloaded_model = AIHubStateModel.model_validate(dumped_data)
        
        assert original_model == reloaded_model

        # Test JSON roundtrip
        json_data = original_model.model_dump_json()
        reloaded_from_json = AIHubStateModel.model_validate_json(json_data)
        assert original_model == reloaded_from_json

    def test_ai_hub_state_deeply_nested_validation_failure(self, valid_ai_hub_state_data):
        """Checks that a validation error in a deeply nested model bubbles up."""
        invalid_data = json.loads(json.dumps(valid_ai_hub_state_data)) # Deep copy

        # Introduce an error deep in the structure
        invalid_data["market_compass"]["segments"][2]["score"] = 99.0 # Invalid score
        
        with pytest.raises(ValidationError) as excinfo:
            AIHubStateModel.model_validate(invalid_data)
        
        # Check that the error message points to the right location
        assert "market_compass" in str(excinfo.value)
        assert "segments" in str(excinfo.value)
        assert "score" in str(excinfo.value)
        assert "Input should be less than or equal to 1" in str(excinfo.value)

    def test_ai_hub_state_with_optional_fields_as_none(self, valid_ai_hub_state_data):
        """Tests the model when optional top-level panels are None."""
        data = valid_ai_hub_state_data.copy()
        data["market_compass"] = None
        data["ai_recommendations"] = None
        
        hub_state = AIHubStateModel.model_validate(data)
        assert hub_state.market_compass is None
        assert hub_state.ai_recommendations is None
        assert hub_state.huihui_monitor is not None


@pytest.mark.performance
def test_ai_hub_state_performance_with_large_dataset(valid_ai_recommendation_item_data):
    """
    A simple performance test to validate a state model with a large number of
    nested items. This is not a benchmark but helps catch major performance regressions.
    """
    # Create a very large list of recommendations
    num_items = 1000
    large_recommendations_list = []
    for i in range(num_items):
        item_data = valid_ai_recommendation_item_data.copy()
        item_data["id"] = f"rec-{i}"
        large_recommendations_list.append(item_data)

    # Build the full state data
    large_state_data = {
        "hub_status": "OK",
        "last_updated": datetime.now().isoformat(),
        "target_symbol": "QQQ",
        "error_message": None,
        "layout_config": {},
        "market_compass": None,
        "ai_recommendations": {
            "panel_config": {
                "id": "ai-recs-qqq",
                "title": "AI-Driven Recommendations",
                "panel_type": "trade_recommendations"
            },
            "recommendations": large_recommendations_list
        },
        "flow_intelligence_metrics": None,
        "volatility_gamma_metrics": None,
        "advanced_options_metrics": None,
        "huihui_monitor": None,
        "raw_analysis_bundle": None
    }

    # The test is simply to validate this large object. Pytest will report the duration.
    # If this takes an unexpectedly long time, it indicates a performance issue.
    try:
        start_time = datetime.now()
        hub_state = AIHubStateModel.model_validate(large_state_data)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        assert len(hub_state.ai_recommendations.recommendations) == num_items
        print(f"\nPerformance test with {num_items} recommendations validated in {duration:.4f} seconds.")
        # Assert that validation is reasonably fast (e.g., under 1 second)
        assert duration < 1.0
    except ValidationError as e:
        pytest.fail(f"Performance test failed validation unexpectedly: {e}")
