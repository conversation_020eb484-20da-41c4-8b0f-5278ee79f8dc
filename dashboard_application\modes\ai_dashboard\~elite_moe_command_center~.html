<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elite MOE Command Center</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');
        
        :root {
            /* Elite Color Palette */
            --primary-bg: #0a0e1a;
            --secondary-bg: #111827;
            --card-bg: rgba(17, 24, 39, 0.8);
            --inner-card-bg: rgba(30, 41, 59, 0.6);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --border-color: rgba(255, 255, 255, 0.1);
            --inner-border-color: rgba(255, 255, 255, 0.08);
            --text-primary: #ffffff;
            --text-secondary: #9ca3af;
            --text-muted: #6b7280;
            
            /* MOE Expert Colors */
            --regime-color: #8b5cf6;
            --flow-color: #06b6d4;
            --intelligence-color: #10b981;
            --orchestrator-color: #f59e0b;
            
            /* Gradients */
            --gradient-regime: linear-gradient(135deg, #8b5cf6, #7c3aed);
            --gradient-flow: linear-gradient(135deg, #06b6d4, #0891b2);
            --gradient-intelligence: linear-gradient(135deg, #10b981, #059669);
            --gradient-orchestrator: linear-gradient(135deg, #f59e0b, #d97706);
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-mono: 'JetBrains Mono', 'SF Mono', monospace;
            
            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-primary);
            background: var(--primary-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        /* Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
            z-index: -1;
        }
        
        /* Header */
        .header {
            padding: var(--spacing-lg) var(--spacing-xl);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-orchestrator);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .status-bar {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--intelligence-color);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* Main Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }
        
        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }
        
        .moe-experts-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }
        
        .health-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-lg);
        }
        
        /* Card Styles */
        .card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.2);
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }
        
        /* Card Header */
        .card-header {
            padding: var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--inner-border-color);
            background: rgba(255, 255, 255, 0.02);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .card-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: var(--spacing-xs);
        }
        
        .card-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        /* Card Content */
        .card-content {
            padding: var(--spacing-xl);
        }
        
        /* Inner Cards */
        .inner-card {
            background: var(--inner-card-bg);
            border: 1px solid var(--inner-border-color);
            border-radius: 12px;
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            transition: all 0.3s ease;
        }
        
        .inner-card:hover {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(255, 255, 255, 0.15);
        }
        
        .inner-card:last-child {
            margin-bottom: 0;
        }
        
        /* MOE Expert Cards */
        .moe-card {
            position: relative;
        }
        
        .moe-card.regime {
            border-color: var(--regime-color);
        }
        
        .moe-card.flow {
            border-color: var(--flow-color);
        }
        
        .moe-card.intelligence {
            border-color: var(--intelligence-color);
        }
        
        .moe-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            border-radius: 16px 16px 0 0;
        }
        
        .moe-card.regime::after {
            background: var(--gradient-regime);
        }
        
        .moe-card.flow::after {
            background: var(--gradient-flow);
        }
        
        .moe-card.intelligence::after {
            background: var(--gradient-intelligence);
        }
        
        /* Expert Status */
        .expert-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .expert-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .expert-avatar.regime {
            background: var(--gradient-regime);
        }
        
        .expert-avatar.flow {
            background: var(--gradient-flow);
        }
        
        .expert-avatar.intelligence {
            background: var(--gradient-intelligence);
        }
        
        .expert-info {
            flex: 1;
        }
        
        .expert-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .expert-confidence {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        /* Compass Container */
        .compass-container {
            position: relative;
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .compass {
            width: 400px;
            height: 400px;
            position: relative;
            border-radius: 50%;
            background: 
                radial-gradient(circle at center, 
                    rgba(255, 255, 255, 0.05) 0%, 
                    rgba(255, 255, 255, 0.02) 50%, 
                    transparent 100%);
            border: 2px solid var(--border-color);
            box-shadow: 
                inset 0 0 50px rgba(0, 0, 0, 0.3),
                0 0 50px rgba(245, 158, 11, 0.1);
        }
        
        .compass-grid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            height: 90%;
        }
        
        .compass-grid::before,
        .compass-grid::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .compass-grid::before {
            width: 100%;
            height: 1px;
        }
        
        .compass-grid::after {
            width: 1px;
            height: 100%;
        }
        
        .compass-shape {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            clip-path: polygon(50% 0%, 93% 25%, 93% 75%, 50% 100%, 7% 75%, 7% 25%);
            background: var(--gradient-orchestrator);
            opacity: 0.8;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(0 0 20px rgba(245, 158, 11, 0.4));
        }
        
        .compass-labels {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .compass-label {
            position: absolute;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .compass-label.top { top: 10px; left: 50%; transform: translateX(-50%); }
        .compass-label.top-right { top: 15%; right: 10px; }
        .compass-label.bottom-right { bottom: 15%; right: 10px; }
        .compass-label.bottom { bottom: 10px; left: 50%; transform: translateX(-50%); }
        .compass-label.bottom-left { bottom: 15%; left: 10px; }
        .compass-label.top-left { top: 15%; left: 10px; }
        
        .compass-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            background: var(--text-primary);
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }
        
        .compass-focus-indicator {
            position: absolute;
            width: 30px;
            height: 30px;
            background: var(--text-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            animation: focusPulse 2s infinite;
        }
        
        @keyframes focusPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* Orchestrator Analysis */
        .orchestrator-analysis {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .signal-display {
            text-align: center;
        }
        
        .signal-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            background: var(--gradient-orchestrator);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .signal-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
        }
        
        .confidence-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }
        
        .confidence-fill {
            height: 100%;
            background: var(--gradient-orchestrator);
            border-radius: 4px;
            transition: width 1s ease;
        }
        
        .confidence-text {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
        }
        
        /* Metric Display */
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .metric-item {
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            font-family: var(--font-mono);
            margin-bottom: var(--spacing-xs);
        }
        
        .metric-value.regime {
            background: var(--gradient-regime);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .metric-value.flow {
            background: var(--gradient-flow);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .metric-value.intelligence {
            background: var(--gradient-intelligence);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .metric-label {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .metric-change {
            font-size: 11px;
            font-weight: 500;
            margin-top: var(--spacing-xs);
        }
        
        .metric-change.positive { color: var(--intelligence-color); }
        .metric-change.negative { color: #ef4444; }
        
        /* Health Cards */
        .health-card {
            text-align: center;
        }
        
        .health-status {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
        }
        
        .health-status.healthy {
            background: var(--gradient-intelligence);
            color: white;
        }
        
        .health-status.warning {
            background: linear-gradient(135deg, #f59e0b, #ea580c);
            color: white;
        }
        
        .health-status.critical {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        
        .health-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }
        
        .health-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .moe-experts-grid {
                grid-template-columns: 1fr;
            }
            
            .health-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: var(--spacing-md);
            }
            
            .health-grid {
                grid-template-columns: 1fr;
            }
            
            .compass {
                width: 300px;
                height: 300px;
            }
            
            .compass-shape {
                width: 150px;
                height: 150px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🎯</div>
                <div class="logo-text">Elite MOE Command Center</div>
            </div>
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-dot"></div>
                    <span>ITS Orchestrator Online</span>
                </div>
                <div class="status-item">
                    <span>Regime: Bull Trending</span>
                </div>
                <div class="status-item">
                    <span>Confidence: 94%</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Row 1: ITS Orchestrator Command Center -->
        <div class="dashboard-grid">
            <!-- ITS Orchestrator Analysis -->
            <div class="card">
                <div class="card-header">
                    <div>
                        <div class="card-title">🎯 ITS Orchestrator</div>
                        <div class="card-subtitle">Supreme command & control</div>
                    </div>
                    <div class="card-badge">SUPREME</div>
                </div>
                
                <div class="card-content">
                    <div class="inner-card">
                        <div class="orchestrator-analysis">
                            <div class="signal-display">
                                <div class="signal-title" id="signalTitle">Momentum Explosion Imminent</div>
                                <div class="signal-subtitle" id="signalAction">Aggressive trend following recommended</div>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" id="confidenceFill" style="width: 94%;"></div>
                                </div>
                                <div class="confidence-text" id="confidenceText">Supreme Confidence: 94%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="inner-card">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div style="text-align: center;">
                                <div style="font-size: 14px; color: #9ca3af; margin-bottom: 0.5rem;">Focus Metric</div>
                                <div style="font-size: 18px; font-weight: 600;" id="focusMetric">MSPI</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 14px; color: #9ca3af; margin-bottom: 0.5rem;">Expert Consensus</div>
                                <div style="font-size: 18px; font-weight: 600;" id="consensus">3/3 Agree</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Market Compass (ITS Orchestrator Controlled) -->
            <div class="card">
                <div class="card-header">
                    <div>
                        <div class="card-title">🧭 Market Compass</div>
                        <div class="card-subtitle">ITS Orchestrator supreme control</div>
                    </div>
                    <div class="card-badge">LIVE</div>
                </div>
                
                <div class="card-content">
                    <div class="compass-container">
                        <div class="compass">
                            <div class="compass-grid"></div>
                            <div class="compass-shape" id="compassShape"></div>
                            <div class="compass-labels">
                                <div class="compass-label top">VAPI-FA</div>
                                <div class="compass-label top-right">DWFD</div>
                                <div class="compass-label bottom-right">VRI 2.0</div>
                                <div class="compass-label bottom">GIB</div>
                                <div class="compass-label bottom-left">MSPI</div>
                                <div class="compass-label top-left">A-DAG</div>
                            </div>
                            <div class="compass-center"></div>
                            <div class="compass-focus-indicator" id="focusIndicator" style="bottom: 25%; left: 25%;">⭐</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Row 2: MOE Expert Containers -->
        <div class="moe-experts-grid">
            <!-- Market Regime Expert -->
            <div class="card moe-card regime">
                <div class="card-header">
                    <div>
                        <div class="card-title">🌊 Market Regime Expert</div>
                        <div class="card-subtitle">Regime analysis & transitions</div>
                    </div>
                    <div class="card-badge">ACTIVE</div>
                </div>
                
                <div class="card-content">
                    <div class="inner-card">
                        <div class="expert-status">
                            <div class="expert-avatar regime">🌊</div>
                            <div class="expert-info">
                                <div class="expert-name">Regime Expert</div>
                                <div class="expert-confidence">Confidence: 89%</div>
                            </div>
                        </div>
                        
                        <div class="metric-grid">
                            <div class="metric-item">
                                <div class="metric-value regime">-2.53</div>
                                <div class="metric-label">VRI 2.0</div>
                                <div class="metric-change negative">-8.7%</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value regime">0.23</div>
                                <div class="metric-label">Transition Risk</div>
                                <div class="metric-change positive">+2.1%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="inner-card">
                        <div style="text-align: center;">
                            <div style="font-size: 14px; color: #9ca3af; margin-bottom: 0.5rem;">Current Regime</div>
                            <div style="font-size: 16px; font-weight: 600; color: #8b5cf6;">Bull Trending</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Options Flow Expert -->
            <div class="card moe-card flow">
                <div class="card-header">
                    <div>
                        <div class="card-title">💧 Options Flow Expert</div>
                        <div class="card-subtitle">Flow dynamics & pressure</div>
                    </div>
                    <div class="card-badge">ACTIVE</div>
                </div>
                
                <div class="card-content">
                    <div class="inner-card">
                        <div class="expert-status">
                            <div class="expert-avatar flow">💧</div>
                            <div class="expert-info">
                                <div class="expert-name">Flow Expert</div>
                                <div class="expert-confidence">Confidence: 92%</div>
                            </div>
                        </div>
                        
                        <div class="metric-grid">
                            <div class="metric-item">
                                <div class="metric-value flow">1.84</div>
                                <div class="metric-label">VAPI-FA</div>
                                <div class="metric-change positive">+12.3%</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value flow">1.96</div>
                                <div class="metric-label">DWFD</div>
                                <div class="metric-change positive">+8.9%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="inner-card">
                        <div style="text-align: center;">
                            <div style="font-size: 14px; color: #9ca3af; margin-bottom: 0.5rem;">Flow Status</div>
                            <div style="font-size: 16px; font-weight: 600; color: #06b6d4;">Strong Bullish</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Market Intelligence Expert -->
            <div class="card moe-card intelligence">
                <div class="card-header">
                    <div>
                        <div class="card-title">🧠 Intelligence Expert</div>
                        <div class="card-subtitle">Sentiment & intelligence</div>
                    </div>
                    <div class="card-badge">ACTIVE</div>
                </div>
                
                <div class="card-content">
                    <div class="inner-card">
                        <div class="expert-status">
                            <div class="expert-avatar intelligence">🧠</div>
                            <div class="expert-info">
                                <div class="expert-name">Intelligence Expert</div>
                                <div class="expert-confidence">Confidence: 87%</div>
                            </div>
                        </div>
                        
                        <div class="metric-grid">
                            <div class="metric-item">
                                <div class="metric-value intelligence">2.31</div>
                                <div class="metric-label">MSPI</div>
                                <div class="metric-change positive">+15.7%</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value intelligence">1.67</div>
                                <div class="metric-label">AOFM</div>
                                <div class="metric-change positive">+11.2%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="inner-card">
                        <div style="text-align: center;">
                            <div style="font-size: 14px; color: #9ca3af; margin-bottom: 0.5rem;">Intelligence Status</div>
                            <div style="font-size: 16px; font-weight: 600; color: #10b981;">High Conviction</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Row 3: System Health -->
        <div class="health-grid">
            <div class="card health-card">
                <div class="card-header">
                    <div class="card-title">📊 Data Pipeline</div>
                </div>
                <div class="card-content">
                    <div class="health-status healthy">✓</div>
                    <div class="health-label">Status</div>
                    <div class="health-value">Healthy</div>
                </div>
            </div>
            
            <div class="card health-card">
                <div class="card-header">
                    <div class="card-title">🤖 MOE Experts</div>
                </div>
                <div class="card-content">
                    <div class="health-status healthy">3</div>
                    <div class="health-label">Online</div>
                    <div class="health-value">3/3 Active</div>
                </div>
            </div>
            
            <div class="card health-card">
                <div class="card-header">
                    <div class="card-title">⚡ Performance</div>
                </div>
                <div class="card-content">
                    <div class="health-status warning">⚠</div>
                    <div class="health-label">Response Time</div>
                    <div class="health-value">1.2s Avg</div>
                </div>
            </div>
            
            <div class="card health-card">
                <div class="card-header">
                    <div class="card-title">📡 Connectivity</div>
                </div>
                <div class="card-content">
                    <div class="health-status healthy">📡</div>
                    <div class="health-label">Network</div>
                    <div class="health-value">Stable</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ITS Orchestrator scenarios
        const orchestratorScenarios = [
            {
                signal: { title: 'Momentum Explosion Imminent', action: 'Aggressive trend following recommended' },
                confidence: 94,
                focusMetric: 'MSPI',
                consensus: '3/3 Agree',
                compassColor: 'linear-gradient(135deg, #10b981, #059669)'
            },
            {
                signal: { title: 'Volatility Expansion Detected', action: 'Prepare for increased movement' },
                confidence: 89,
                focusMetric: 'VRI 2.0',
                consensus: '2/3 Agree',
                compassColor: 'linear-gradient(135deg, #ef4444, #dc2626)'
            },
            {
                signal: { title: 'Premium Selling Window', action: 'Consider selling options premium' },
                confidence: 76,
                focusMetric: 'GIB',
                consensus: '3/3 Agree',
                compassColor: 'linear-gradient(135deg, #3b82f6, #2563eb)'
            }
        ];
        
        function updateOrchestrator(scenario) {
            const signalTitle = document.getElementById('signalTitle');
            const signalAction = document.getElementById('signalAction');
            const confidenceFill = document.getElementById('confidenceFill');
            const confidenceText = document.getElementById('confidenceText');
            const focusMetric = document.getElementById('focusMetric');
            const consensus = document.getElementById('consensus');
            const compassShape = document.getElementById('compassShape');
            
            // Update orchestrator display
            signalTitle.textContent = scenario.signal.title;
            signalAction.textContent = scenario.signal.action;
            confidenceFill.style.width = `${scenario.confidence}%`;
            confidenceText.textContent = `Supreme Confidence: ${scenario.confidence}%`;
            focusMetric.textContent = scenario.focusMetric;
            consensus.textContent = scenario.consensus;
            
            // Update compass
            compassShape.style.background = scenario.compassColor;
            signalTitle.style.background = scenario.compassColor;
            signalTitle.style.webkitBackgroundClip = 'text';
            signalTitle.style.webkitTextFillColor = 'transparent';
            confidenceFill.style.background = scenario.compassColor;
        }
        
        let currentScenario = 0;
        
        // Cycle through scenarios every 6 seconds
        setInterval(() => {
            const scenario = orchestratorScenarios[currentScenario];
            updateOrchestrator(scenario);
            currentScenario = (currentScenario + 1) % orchestratorScenarios.length;
        }, 6000);
        
        // Initialize with first scenario
        updateOrchestrator(orchestratorScenarios[0]);
    </script>
</body>
</html>

