#!/usr/bin/env python
"""
Debug runner for EOTS v2.5 - captures and displays errors step by step
"""

import sys
import os
import traceback
import logging

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='[%(asctime)s] [%(levelname)s] [%(name)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

def debug_step(step_name, func):
    """Execute a step and catch any errors"""
    try:
        logger.info(f"🔍 Starting: {step_name}")
        result = func()
        logger.info(f"✅ Completed: {step_name}")
        return result
    except Exception as e:
        logger.error(f"❌ Failed: {step_name}")
        logger.error(f"Error: {type(e).__name__}: {str(e)}")
        logger.error(f"Traceback:\n{traceback.format_exc()}")
        raise

def main():
    """Debug the EOTS v2.5 startup process step by step"""
    
    # Step 1: Load environment
    def load_env():
        from dotenv import load_dotenv
        env_path = os.path.join(project_root, '.env')
        return load_dotenv(env_path)
    
    debug_step("Load Environment Variables", load_env)
    
    # Step 2: Import root package
    def import_root():
        import elite_options_system_v2_5
        return elite_options_system_v2_5
    
    debug_step("Import Root Package", import_root)
    
    # Step 3: Import config manager
    def import_config():
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        return ConfigManagerV2_5()
    
    config_manager = debug_step("Import and Initialize Config Manager", import_config)
    
    # Step 4: Import elite config
    def import_elite_config():
        from data_models.elite_config_models import EliteConfig
        return config_manager.config.elite_config or EliteConfig()
    
    elite_config = debug_step("Import Elite Config", import_elite_config)
    
    # Step 5: Import database manager
    def import_db_manager():
        from data_management.database_manager_v2_5 import DatabaseManagerV2_5
        return DatabaseManagerV2_5(config_manager=config_manager)
    
    db_manager = debug_step("Import Database Manager", import_db_manager)
    
    # Step 6: Import cache manager
    def import_cache_manager():
        from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5
        return EnhancedCacheManagerV2_5()
    
    cache_manager = debug_step("Import Cache Manager", import_cache_manager)
    
    # Step 7: Import orchestrator
    def import_orchestrator():
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        return ITSOrchestratorV2_5(
            config_manager=config_manager,
            db_manager=db_manager,
            enhanced_cache=cache_manager,
            elite_config=elite_config
        )
    
    orchestrator = debug_step("Import and Initialize Orchestrator", import_orchestrator)
    
    # Step 8: Import dashboard
    def import_dashboard():
        from dashboard_application.app_main import main
        return main
    
    main_func = debug_step("Import Dashboard Main", import_dashboard)
    
    # Step 9: Try to start dashboard (with timeout)
    logger.info("🚀 Attempting to start dashboard...")
    logger.info("⚠️  This may hang - if so, we'll identify where...")
    
    try:
        main_func(elite_config=elite_config)
    except KeyboardInterrupt:
        logger.info("🛑 Dashboard startup interrupted by user")
    except Exception as e:
        logger.error(f"❌ Dashboard startup failed: {type(e).__name__}: {str(e)}")
        logger.error(f"Traceback:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
