/* =============================================================================
   ELITE OPTIONS TRADING SYSTEM - TABLE COMPONENTS
   Data tables, order books, trade history, and financial data grids
============================================================================= */

/* ==========================================================================
   BASE TABLE STYLES
========================================================================== */

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-container {
  position: relative;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table-wrapper {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 100%;
}

.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-full);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--border-accent);
}

/* ==========================================================================
   TABLE HEADER
========================================================================== */

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.table-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.table-subtitle {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: 0;
  margin-top: var(--space-xs);
}

.table-actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.table-search {
  position: relative;
  width: 250px;
}

.table-search input {
  width: 100%;
  padding: var(--space-sm) var(--space-md) var(--space-sm) 36px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: all var(--duration-fast) var(--ease-out);
}

.table-search input:focus {
  border-color: var(--accent-primary);
  box-shadow: var(--focus-ring);
}

.table-search-icon {
  position: absolute;
  left: var(--space-sm);
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: var(--text-muted);
  pointer-events: none;
}

/* ==========================================================================
   TABLE HEAD
========================================================================== */

.table thead {
  background-color: var(--bg-tertiary);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.table th {
  padding: var(--space-md) var(--space-lg);
  text-align: left;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-primary);
  white-space: nowrap;
  position: relative;
  user-select: none;
}

.table th.sortable {
  cursor: pointer;
  transition: background-color var(--duration-fast) var(--ease-out);
}

.table th.sortable:hover {
  background-color: var(--bg-hover);
}

.table th.sorted {
  background-color: var(--accent-primary-alpha);
  color: var(--accent-primary);
}

.table th.text-right {
  text-align: right;
}

.table th.text-center {
  text-align: center;
}

/* Sort indicators */
.sort-indicator {
  position: absolute;
  right: var(--space-sm);
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  opacity: 0;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.table th.sortable:hover .sort-indicator,
.table th.sorted .sort-indicator {
  opacity: 1;
}

.sort-indicator.asc {
  transform: translateY(-50%) rotate(0deg);
}

.sort-indicator.desc {
  transform: translateY(-50%) rotate(180deg);
}

/* ==========================================================================
   TABLE BODY
========================================================================== */

.table tbody {
  background-color: var(--bg-secondary);
}

.table td {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-secondary);
  font-size: var(--text-sm);
  color: var(--text-primary);
  vertical-align: middle;
}

.table tr {
  transition: background-color var(--duration-fast) var(--ease-out);
}

.table tbody tr:hover {
  background-color: var(--bg-hover);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

.table td.text-right {
  text-align: right;
}

.table td.text-center {
  text-align: center;
}

.table td.text-mono {
  font-family: var(--font-mono);
  font-weight: var(--font-medium);
}

/* ==========================================================================
   TABLE VARIANTS
========================================================================== */

/* Striped table */
.table-striped tbody tr:nth-child(even) {
  background-color: var(--bg-tertiary);
}

.table-striped tbody tr:nth-child(even):hover {
  background-color: var(--bg-hover);
}

/* Bordered table */
.table-bordered {
  border: 1px solid var(--border-primary);
}

.table-bordered th,
.table-bordered td {
  border-right: 1px solid var(--border-secondary);
}

.table-bordered th:last-child,
.table-bordered td:last-child {
  border-right: none;
}

/* Compact table */
.table-compact th,
.table-compact td {
  padding: var(--space-sm) var(--space-md);
}

/* Large table */
.table-large th,
.table-large td {
  padding: var(--space-lg) var(--space-xl);
}

/* ==========================================================================
   TRADING-SPECIFIC TABLES
========================================================================== */

/* Order Book */
.order-book {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  background-color: var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.order-book-side {
  background-color: var(--bg-secondary);
}

.order-book-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  background-color: var(--bg-tertiary);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wider);
}

.order-book-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--space-sm);
  padding: var(--space-xs) var(--space-lg);
  font-size: var(--text-sm);
  font-family: var(--font-mono);
  transition: background-color var(--duration-fast) var(--ease-out);
  cursor: pointer;
  position: relative;
}

.order-book-row:hover {
  background-color: var(--bg-hover);
}

.order-book-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  transition: width var(--duration-fast) var(--ease-out);
  z-index: 1;
}

.order-book-bids .order-book-row::before {
  background-color: var(--positive-alpha);
}

.order-book-asks .order-book-row::before {
  background-color: var(--negative-alpha);
}

.order-book-bids .order-book-row:hover::before {
  width: 100%;
}

.order-book-asks .order-book-row:hover::before {
  width: 100%;
}

.order-book-price {
  color: var(--text-primary);
  font-weight: var(--font-semibold);
  position: relative;
  z-index: 2;
}

.order-book-bids .order-book-price {
  color: var(--positive);
}

.order-book-asks .order-book-price {
  color: var(--negative);
}

.order-book-size,
.order-book-total {
  color: var(--text-secondary);
  position: relative;
  z-index: 2;
}

.order-book-spread {
  grid-column: 1 / -1;
  padding: var(--space-md) var(--space-lg);
  text-align: center;
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-primary);
  border-bottom: 1px solid var(--border-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-muted);
}

/* Trade History */
.trade-history {
  max-height: 400px;
  overflow-y: auto;
}

.trade-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--text-sm);
  border-bottom: 1px solid var(--border-secondary);
  transition: background-color var(--duration-fast) var(--ease-out);
}

.trade-row:hover {
  background-color: var(--bg-hover);
}

.trade-row:last-child {
  border-bottom: none;
}

.trade-price {
  font-family: var(--font-mono);
  font-weight: var(--font-semibold);
}

.trade-price.buy {
  color: var(--positive);
}

.trade-price.sell {
  color: var(--negative);
}

.trade-size,
.trade-total {
  font-family: var(--font-mono);
  color: var(--text-secondary);
}

.trade-time {
  color: var(--text-muted);
  font-size: var(--text-xs);
}

/* Portfolio Table */
.portfolio-table .portfolio-symbol {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.portfolio-table .portfolio-change {
  font-family: var(--font-mono);
  font-weight: var(--font-semibold);
}

.portfolio-table .portfolio-change.positive {
  color: var(--positive);
}

.portfolio-table .portfolio-change.negative {
  color: var(--negative);
}

.portfolio-table .portfolio-change.neutral {
  color: var(--text-muted);
}

.portfolio-table .portfolio-value {
  font-family: var(--font-mono);
  font-weight: var(--font-medium);
}

/* ==========================================================================
   TABLE STATES
========================================================================== */

.table-loading {
  position: relative;
  pointer-events: none;
}

.table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--bg-secondary-rgb), 0.8);
  backdrop-filter: var(--backdrop-blur);
  z-index: var(--z-overlay);
}

.table-loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--accent-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  z-index: var(--z-modal);
}

.table-empty {
  text-align: center;
  padding: var(--space-xl);
  color: var(--text-muted);
}

.table-empty-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-md);
  opacity: 0.5;
}

.table-empty-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-sm);
}

.table-empty-description {
  font-size: var(--text-sm);
  max-width: 300px;
  margin: 0 auto;
}

/* ==========================================================================
   TABLE PAGINATION
========================================================================== */

.table-pagination {
  display: flex;
  align-items: center;
  justify-content: between;
  padding: var(--space-lg);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.table-pagination-info {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.table-pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-left: auto;
}

.table-pagination-btn {
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.table-pagination-btn:hover:not(:disabled) {
  background-color: var(--bg-hover);
  border-color: var(--accent-primary);
}

.table-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table-pagination-btn.active {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.table-page-size {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.table-page-size select {
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

/* ==========================================================================
   TABLE FILTERS
========================================================================== */

.table-filters {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-tertiary);
  flex-wrap: wrap;
}

.table-filter {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.table-filter-label {
  font-size: var(--text-sm);
  color: var(--text-muted);
  white-space: nowrap;
}

.table-filter select,
.table-filter input {
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--text-sm);
  min-width: 120px;
}

.table-filter-clear {
  padding: var(--space-xs) var(--space-sm);
  background: none;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  color: var(--text-muted);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.table-filter-clear:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

/* ==========================================================================
   TABLE SELECTION
========================================================================== */

.table-select-all {
  width: 16px;
  height: 16px;
}

.table-select-row {
  width: 16px;
  height: 16px;
}

.table-selected {
  background-color: var(--accent-primary-alpha) !important;
}

.table-selection-actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  background-color: var(--accent-primary-alpha);
  border-bottom: 1px solid var(--accent-primary);
  color: var(--accent-primary);
}

.table-selection-count {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.table-selection-btn {
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--accent-primary);
  border: none;
  border-radius: var(--radius-sm);
  color: white;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.table-selection-btn:hover {
  background-color: var(--accent-secondary);
}

/* ==========================================================================
   RESPONSIVE TABLES
========================================================================== */

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-md);
  }
  
  .table-actions {
    justify-content: space-between;
  }
  
  .table-search {
    width: 100%;
    max-width: none;
  }
  
  .table-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-filter {
    justify-content: space-between;
  }
  
  .table-pagination {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .table-pagination-controls {
    margin-left: 0;
    justify-content: center;
  }
  
  /* Stack table on mobile */
  .table-responsive {
    display: block;
  }
  
  .table-responsive .table {
    display: block;
  }
  
  .table-responsive thead {
    display: none;
  }
  
  .table-responsive tbody {
    display: block;
  }
  
  .table-responsive tr {
    display: block;
    margin-bottom: var(--space-md);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--space-md);
  }
  
  .table-responsive td {
    display: block;
    text-align: left !important;
    border: none;
    padding: var(--space-xs) 0;
  }
  
  .table-responsive td::before {
    content: attr(data-label) ': ';
    font-weight: var(--font-semibold);
    color: var(--text-muted);
    display: inline-block;
    width: 100px;
    flex-shrink: 0;
  }
  
  .table-responsive td {
    display: flex;
    align-items: center;
  }
  
  /* Order book mobile */
  .order-book {
    grid-template-columns: 1fr;
  }
  
  .order-book-header,
  .order-book-row {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-md);
  }
  
  .order-book-total {
    display: none;
  }
}

@media (max-width: 480px) {
  .table-container {
    margin: 0 calc(-1 * var(--space-md));
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .table-header,
  .table-filters,
  .table-pagination {
    padding: var(--space-md);
  }
  
  .order-book-header,
  .order-book-row {
    padding: var(--space-sm) var(--space-md);
  }
  
  .trade-row {
    padding: var(--space-sm) var(--space-md);
    grid-template-columns: 1fr 1fr;
  }
  
  .trade-total,
  .trade-time {
    display: none;
  }
}

/* ==========================================================================
   TABLE ANIMATIONS
========================================================================== */

@keyframes tableRowFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-row-new {
  animation: tableRowFadeIn var(--duration-fast) var(--ease-out);
}

.table-row-updated {
  background-color: var(--accent-primary-alpha);
  animation: tableRowFadeIn var(--duration-fast) var(--ease-out);
}

/* ==========================================================================
   ACCESSIBILITY
========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .table tr,
  .order-book-row,
  .trade-row {
    transition: none;
  }
  
  .table-loading-spinner {
    animation: none;
  }
}

.table th:focus,
.table td:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: -2px;
}

/* ==========================================================================
   TABLE UTILITIES
========================================================================== */

.table-fixed {
  table-layout: fixed;
}

.table-auto {
  table-layout: auto;
}

.table-nowrap {
  white-space: nowrap;
}

.table-break {
  word-break: break-word;
}

.table-sticky-header thead {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.table-sticky-column {
  position: sticky;
  left: 0;
  background-color: var(--bg-secondary);
  z-index: var(--z-sticky);
}

.table-highlight-row:hover {
  background-color: var(--accent-primary-alpha) !important;
}

.table-dense th,
.table-dense td {
  padding: var(--space-xs) var(--space-sm);
}

.table-spacious th,
.table-spacious td {
  padding: var(--space-xl) var(--space-2xl);
}