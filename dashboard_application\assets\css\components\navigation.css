/* =============================================================================
   ELITE OPTIONS TRADING SYSTEM - NAVIGATION COMPONENTS
   Sidebar, header, and menu systems based on custom dashboard analysis
============================================================================= */

/* ==========================================================================
   SIDEBAR NAVIGATION
========================================================================== */

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  z-index: var(--z-fixed);
  transition: all var(--duration-normal) var(--ease-out);
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-collapsed {
  width: var(--sidebar-width-collapsed);
}

.sidebar-hidden {
  transform: translateX(-100%);
}

/* Sidebar Header */
.sidebar-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  min-height: var(--header-height);
}

.sidebar-logo {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
  color: var(--accent-primary);
}

.sidebar-brand {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  white-space: nowrap;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.sidebar-collapsed .sidebar-brand {
  opacity: 0;
  pointer-events: none;
}

/* Sidebar Content */
.sidebar-content {
  padding: var(--space-lg) 0;
  height: calc(100vh - var(--header-height));
  display: flex;
  flex-direction: column;
}

.sidebar-nav {
  flex: 1;
  padding: 0 var(--space-md);
}

.sidebar-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-secondary);
  margin-top: auto;
}

/* Navigation Menu */
.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-section {
  margin-bottom: var(--space-xl);
}

.nav-section:last-child {
  margin-bottom: 0;
}

.nav-section-title {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wider);
  padding: 0 var(--space-md) var(--space-sm) var(--space-md);
  margin-bottom: var(--space-sm);
  transition: opacity var(--duration-fast) var(--ease-out);
}

.sidebar-collapsed .nav-section-title {
  opacity: 0;
  pointer-events: none;
}

.nav-item {
  margin-bottom: var(--space-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-sm) var(--space-md);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-out);
  position: relative;
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
}

.nav-link:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  transform: translateX(2px);
}

.nav-link.active {
  background-color: var(--accent-primary-alpha);
  color: var(--accent-primary);
  border-left: 3px solid var(--accent-primary);
  padding-left: calc(var(--space-md) - 3px);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--accent-primary);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  transition: color var(--duration-fast) var(--ease-out);
}

.nav-text {
  flex: 1;
  white-space: nowrap;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.sidebar-collapsed .nav-text {
  opacity: 0;
  pointer-events: none;
}

.nav-badge {
  background-color: var(--accent-secondary);
  color: var(--bg-primary);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  text-align: center;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.sidebar-collapsed .nav-badge {
  opacity: 0;
  pointer-events: none;
}

/* Submenu */
.nav-submenu {
  list-style: none;
  padding: 0;
  margin: var(--space-xs) 0 0 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--duration-normal) var(--ease-out);
}

.nav-item.expanded .nav-submenu {
  max-height: 300px;
}

.nav-submenu .nav-link {
  padding-left: calc(var(--space-md) + 20px + var(--space-md));
  font-size: var(--text-xs);
  color: var(--text-muted);
}

.nav-submenu .nav-link:hover {
  color: var(--text-secondary);
}

.nav-submenu .nav-link.active {
  color: var(--accent-primary);
  background-color: var(--accent-primary-alpha);
}

/* ==========================================================================
   HEADER NAVIGATION
========================================================================== */

.header {
  position: fixed;
  top: 0;
  left: var(--sidebar-width);
  right: 0;
  height: var(--header-height);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  z-index: var(--z-sticky);
  transition: left var(--duration-normal) var(--ease-out);
  display: flex;
  align-items: center;
  padding: 0 var(--space-xl);
}

.sidebar-collapsed + .header {
  left: var(--sidebar-width-collapsed);
}

.sidebar-hidden + .header {
  left: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-lg);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.header-subtitle {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: 0;
}

/* Header Controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.header-search {
  position: relative;
  width: 300px;
}

.header-search input {
  width: 100%;
  padding: var(--space-sm) var(--space-md) var(--space-sm) 40px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: all var(--duration-fast) var(--ease-out);
}

.header-search input:focus {
  border-color: var(--accent-primary);
  box-shadow: var(--focus-ring);
  background-color: var(--bg-elevated);
}

.header-search-icon {
  position: absolute;
  left: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: var(--text-muted);
  pointer-events: none;
}

/* Market Status Indicator */
.market-status {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.market-status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  animation: pulse 2s infinite;
}

.market-status.open .market-status-dot {
  background-color: var(--positive);
}

.market-status.closed .market-status-dot {
  background-color: var(--negative);
}

.market-status.pre-market .market-status-dot {
  background-color: var(--warning);
}

/* ==========================================================================
   MOBILE NAVIGATION
========================================================================== */

.mobile-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  z-index: var(--z-fixed);
  padding: var(--space-sm) 0;
}

.mobile-nav-menu {
  display: flex;
  justify-content: space-around;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-item {
  flex: 1;
}

.mobile-nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm);
  color: var(--text-muted);
  text-decoration: none;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  transition: color var(--duration-fast) var(--ease-out);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--accent-primary);
}

.mobile-nav-icon {
  width: 24px;
  height: 24px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  padding: var(--space-sm);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: background-color var(--duration-fast) var(--ease-out);
}

.mobile-menu-toggle:hover {
  background-color: var(--bg-hover);
}

.mobile-menu-toggle-icon {
  width: 24px;
  height: 24px;
}

/* ==========================================================================
   BREADCRUMBS
========================================================================== */

.breadcrumbs {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.breadcrumb-link {
  color: var(--text-muted);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-out);
}

.breadcrumb-link:hover {
  color: var(--accent-primary);
}

.breadcrumb-separator {
  width: 12px;
  height: 12px;
  color: var(--text-muted);
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

/* ==========================================================================
   TABS NAVIGATION
========================================================================== */

.tabs {
  border-bottom: 1px solid var(--border-primary);
}

.tabs-list {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: var(--space-md);
}

.tabs-item {
  position: relative;
}

.tabs-link {
  display: block;
  padding: var(--space-md) var(--space-lg);
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-medium);
  border-bottom: 2px solid transparent;
  transition: all var(--duration-fast) var(--ease-out);
}

.tabs-link:hover {
  color: var(--text-primary);
  border-bottom-color: var(--border-accent);
}

.tabs-link.active {
  color: var(--accent-primary);
  border-bottom-color: var(--accent-primary);
}

/* ==========================================================================
   RESPONSIVE NAVIGATION
========================================================================== */

@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .header {
    left: 0;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .header-search {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 var(--space-lg);
  }
  
  .header-center {
    display: none;
  }
  
  .header-search {
    width: 150px;
  }
  
  .header-search input {
    font-size: var(--text-xs);
    padding: var(--space-xs) var(--space-sm) var(--space-xs) 32px;
  }
  
  .mobile-nav {
    display: block;
  }
  
  .tabs-list {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .tabs-list::-webkit-scrollbar {
    display: none;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0 var(--space-md);
  }
  
  .header-title {
    font-size: var(--text-lg);
  }
  
  .header-search {
    display: none;
  }
  
  .sidebar {
    width: 100vw;
  }
  
  .sidebar-header {
    padding: var(--space-md);
  }
  
  .nav-link {
    padding: var(--space-md);
  }
}

/* ==========================================================================
   NAVIGATION ANIMATIONS
========================================================================== */

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.nav-slide-in {
  animation: slideInLeft var(--duration-normal) var(--ease-out);
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* ==========================================================================
   NAVIGATION OVERLAY
========================================================================== */

.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-out);
}

.nav-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ==========================================================================
   ACCESSIBILITY ENHANCEMENTS
========================================================================== */

@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .header,
  .nav-link,
  .tabs-link {
    transition: none;
  }
  
  .nav-link:hover {
    transform: none;
  }
}

.nav-link:focus,
.tabs-link:focus,
.mobile-nav-link:focus {
  outline: none;
  box-shadow: var(--focus-ring);
  border-radius: var(--radius-sm);
}

/* ==========================================================================
   NAVIGATION UTILITIES
========================================================================== */

.nav-divider {
  height: 1px;
  background-color: var(--border-secondary);
  margin: var(--space-md) var(--space-md);
}

.nav-spacer {
  height: var(--space-lg);
}

.nav-sticky {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.nav-fixed {
  position: fixed;
  z-index: var(--z-fixed);
}