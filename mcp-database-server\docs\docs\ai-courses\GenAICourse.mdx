---
sidebar_position: 1
---

import YouTubeVideoEmbed from '@site/src/components/HomepageFeatures/YouTubeVideoEmbed';

# 🤖 Using Generative AI in Software Automation Testing

<div align="center">
  <YouTubeVideoEmbed videoId="Y7xkCWvUKEk" />
</div>
---

:::info 💡 **Note**
All the courses are available on Udemy, and they almost always have a `coupon code` available.  
For discounts, please feel free to reach out at **[<EMAIL>](mailto:<EMAIL>)**.

🎯 **Course Link:**  
[Generative AI in Software Automation Testing](https://www.udemy.com/course/generative-ai-in-software-automation-testing/)
:::

---

## 📚 **Course Description**

This course is crafted for everyone, whether you're new to Software Testing or an experienced professional. Unlock the full potential of **Generative AI** and transform your testing process into something **faster**, **smarter**, and **more efficient**.

### 🚀 **What You’ll Master**

- **🧠 Introduction to Generative AI:**  
  Understand the foundations of Gen AI and its role in Software Testing.

- **💻 Running Large Language Models (LLMs) Locally:**  
  Learn how to run models on your machine without paying for external services.

- **📝 Manual Testing with Gen AI:**  
  Generate manual test cases, test data, and test requirements using grounded Models with the power of AI and RAG.

- **🤖 Automated UI Testing:**  
  Leverage AI to write, refactor, and optimize automated tests for UI applications.

- **🎭 Playwright UI Testing:**  
  Use Playwright and AI-driven tools to create smart test scripts and handle complex workflows.

- **🚫 No-code Automation with TestRigor:**  
  Create powerful automation suites in plain English, even automating SMS, phone calls, and intricate tables.

- **🔗 API Testing:**  
  Harness PostBots and Gen AI to streamline API testing.

- **🧬 Using Gen AI APIs:**  
  Add intelligence to your Test Automation code using OpenAI APIs.

- **📍 Model Context Protocol (MCP):**  
  Run Playwright tests for UI and APIs by leveraging the power of MCP.

---

By the end of this course, you'll have a deep understanding of how **Generative AI** can supercharge your testing process. With hands-on experience, you'll be able to use **AI-enhanced tools** and **LLMs** to simplify complex testing tasks, making your work smoother and more efficient.


