{"name": "@executeautomation/database-server", "version": "1.1.0", "description": "MCP server for interacting with SQLite and SQL Server databases by ExecuteAutomation", "license": "MIT", "author": "ExecuteAutomation, Ltd (https://executeautomation.com)", "homepage": "https://github.com/executeautomation/mcp-database-server", "bugs": "https://github.com/executeautomation/database-server/issues", "type": "module", "bin": {"ea-database-server": "dist/src/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/src/index.js", "prepare": "npm run build", "watch": "tsc --watch", "start": "node dist/src/index.js", "dev": "tsc && node dist/src/index.js", "example": "node examples/example.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@modelcontextprotocol/sdk": "1.9.0", "mssql": "11.0.1", "mysql2": "^3.14.1", "pg": "^8.11.3", "sqlite3": "5.1.7"}, "devDependencies": {"@types/mssql": "^9.1.5", "@types/pg": "^8.11.13", "@types/sqlite3": "5.1.0", "rimraf": "^5.0.5", "shx": "0.4.0", "typescript": "5.8.3"}}