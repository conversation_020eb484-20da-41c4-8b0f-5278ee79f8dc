/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #3578e5;
  --ifm-color-primary-dark: #2160c4;
  --ifm-color-primary-darker: #1a56b9;
  --ifm-color-primary-darkest: #144098;
  --ifm-color-primary-light: #4889e8;
  --ifm-color-primary-lighter: #5a96eb;
  --ifm-color-primary-lightest: #80aff0;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #4e89e8;
  --ifm-color-primary-dark: #3077e4;
  --ifm-color-primary-darker: #226fe3;
  --ifm-color-primary-darkest: #1957c0;
  --ifm-color-primary-light: #6c9bec;
  --ifm-color-primary-lighter: #7ca6ee;
  --ifm-color-primary-lightest: #a8c4f4;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}
