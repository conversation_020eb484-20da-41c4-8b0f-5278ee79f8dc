# core_analytics_engine/eots_metrics/core_calculator.py
    def _calculate_td_gib_optimized_v2_5(self, raw_und_data: RawUnderlyingDataCombinedV2_5, net_cust_gamma_flow: Optional[float] = None) -> float:
        """TD_GIB calculation - now accepts calculated gamma flow as parameter"""
        try:
            # Use provided gamma flow or try to calculate it
            if net_cust_gamma_flow is not None:
                gamma_flow = float(net_cust_gamma_flow)
            else:
                # Try to calculate from raw data components
                try:
                    gamma_buy = (self._require_pydantic_field(raw_und_data, 'gammas_call_buy', 'gamma call buy') +
                               self._require_pydantic_field(raw_und_data, 'gammas_put_buy', 'gamma put buy'))
                    gamma_sell = (self._require_pydantic_field(raw_und_data, 'gammas_call_sell', 'gamma call sell') +
                                self._require_pydantic_field(raw_und_data, 'gammas_put_sell', 'gamma put sell'))
                    gamma_flow = float(gamma_buy - gamma_sell)
                except Exception:
                    # If we can't calculate it, use a simplified approach
                    self.logger.warning("Could not calculate net customer gamma flow for TD_GIB, using simplified calculation")
                    gamma_flow = 0.0  # Simplified fallback

            day_volume = self._require_pydantic_field(raw_und_data, 'day_volume', 'daily volume')
            day_volume = float(day_volume)

            volume_factor = max(1.0, day_volume / 1000000)

            td_gib = gamma_flow * volume_factor / 10000.0
            return self._bound_value(td_gib, -1000, 1000)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating TD_GIB: {e}")
            raise ValueError(f"CRITICAL: TD_GIB calculation failed - cannot return fake 0.0 value! Error: {e}") from e
"""
EOTS Core Calculator - Consolidated Base Utilities + Foundational Metrics

Consolidates:
- base_calculator.py: Core utilities, caching, validation
- foundational_metrics.py: Tier 1 foundational metrics

Optimizations:
- Unified caching strategy
- Streamlined utility functions
- Integrated foundational calculations
- Eliminated redundant base classes
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union, Set, TypeVar
from datetime import datetime, date, timezone
from pydantic import BaseModel, Field, ConfigDict
from collections import deque

# Import necessary schemas
from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5, CacheLevel
from data_models import ProcessedUnderlyingAggregatesV2_5, RawUnderlyingDataCombinedV2_5

logger = logging.getLogger(__name__)
EPSILON = 1e-9
T = TypeVar('T')

# =============================================================================
# STRICT PYDANTIC V2 MODELS - NO DICT[STR, ANY] PATTERNS
# =============================================================================

class AdditionalDataModel(BaseModel):
    """Pydantic model for additional data fields - replaces Dict[str, Any]"""
    custom_metrics: Optional[Dict[str, float]] = Field(default_factory=dict, description="Custom metric values")
    calculation_flags: Optional[Dict[str, bool]] = Field(default_factory=dict, description="Calculation flags")
    metadata_tags: Optional[List[str]] = Field(default_factory=list, description="Metadata tags")
    source_identifiers: Optional[List[str]] = Field(default_factory=list, description="Data source identifiers")
    processing_notes: Optional[List[str]] = Field(default_factory=list, description="Processing notes")
    
    model_config = ConfigDict(extra='forbid')

class ValidationResultsModel(BaseModel):
    """Pydantic model for validation results - replaces Dict[str, Any]"""
    validation_passed: bool = Field(default=True, description="Overall validation status")
    error_messages: List[str] = Field(default_factory=list, description="Validation error messages")
    warning_messages: List[str] = Field(default_factory=list, description="Validation warning messages")
    bounds_checks: Dict[str, bool] = Field(default_factory=dict, description="Bounds check results")
    data_quality_score: Optional[float] = Field(default=None, description="Overall data quality score")
    validation_timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Validation timestamp")
    
    model_config = ConfigDict(extra='forbid')
    
    def update(self, **kwargs):
        """Update validation results"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

class CacheDataModel(BaseModel):
    """Pydantic model for cache data - replaces Dict[str, Any]"""
    metric_values: Dict[str, float] = Field(default_factory=dict, description="Cached metric values")
    timestamps: Dict[str, datetime] = Field(default_factory=dict, description="Value timestamps")
    expiry_times: Dict[str, datetime] = Field(default_factory=dict, description="Cache expiry times")
    hit_counts: Dict[str, int] = Field(default_factory=dict, description="Cache hit counts")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Last cache update")
    
    model_config = ConfigDict(extra='forbid')

class UnderlyingDataInput(BaseModel):
    """Pydantic model for underlying data input to replace raw Dict usage"""
    symbol: str = Field(..., description="Underlying symbol")
    price: float = Field(..., description="Current underlying price")

    # Basic market data
    day_volume: Optional[float] = Field(None, description="Daily volume")
    u_volatility: Optional[float] = Field(None, description="Underlying volatility")

    # Greek flows
    call_gxoi: Optional[float] = Field(None, description="Call gamma x open interest")
    put_gxoi: Optional[float] = Field(None, description="Put gamma x open interest")
    net_cust_delta_flow_und: Optional[float] = Field(None, description="Net customer delta flow")
    net_cust_gamma_flow_und: Optional[float] = Field(None, description="Net customer gamma flow")
    net_cust_vega_flow_und: Optional[float] = Field(None, description="Net customer vega flow")
    net_cust_theta_flow_und: Optional[float] = Field(None, description="Net customer theta flow")

    # Flow metrics
    net_value_flow_5m_und: Optional[float] = Field(None, description="5-minute net value flow")
    net_vol_flow_5m_und: Optional[float] = Field(None, description="5-minute net volume flow")
    net_value_flow_15m_und: Optional[float] = Field(None, description="15-minute net value flow")
    net_vol_flow_15m_und: Optional[float] = Field(None, description="15-minute net volume flow")
    net_value_flow_30m_und: Optional[float] = Field(None, description="30-minute net value flow")
    net_vol_flow_30m_und: Optional[float] = Field(None, description="30-minute net volume flow")

    # Additional fields for extensibility - STRICT PYDANTIC V2
    additional_data: AdditionalDataModel = Field(default_factory=AdditionalDataModel, description="Additional data fields")

    model_config = ConfigDict(extra='allow')  # Allow extra fields for flexibility

class FoundationalMetricsOutput(BaseModel):
    """Pydantic model for foundational metrics output"""
    # Input data (preserved)
    symbol: str = Field(..., description="Underlying symbol")
    price: float = Field(..., description="Current underlying price")

    # Calculated foundational metrics
    gib_oi_based_und: Optional[float] = Field(None, description="GIB from open interest")
    hp_eod_und: Optional[float] = Field(None, description="End-of-Day Hedging Pressure")
    td_gib_und: Optional[float] = Field(None, description="Traded Dealer Gamma Imbalance")

    # Net customer flows (calculated)
    net_cust_delta_flow_und: Optional[float] = Field(None, description="Net customer delta flow")
    net_cust_gamma_flow_und: Optional[float] = Field(None, description="Net customer gamma flow")
    net_cust_vega_flow_und: Optional[float] = Field(None, description="Net customer vega flow")
    net_cust_theta_flow_und: Optional[float] = Field(None, description="Net customer theta flow")

    # Validation results
    validation_passed: bool = Field(True, description="Whether validation passed")
    validation_errors: List[str] = Field(default_factory=list, description="Validation error messages")

    # Calculation metadata
    calculation_timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When calculated")

    model_config = ConfigDict(extra='allow')  # Allow extra fields for extensibility

class MetricCalculationState(BaseModel):
    """Unified state tracking for all metric calculations"""
    current_symbol: Optional[str] = Field(None, description="Current symbol being processed")
    calculation_timestamp: Optional[datetime] = Field(None, description="Timestamp of last calculation")
    metrics_completed: Set[str] = Field(default_factory=set, description="Set of completed metrics")
    validation_results: ValidationResultsModel = Field(default_factory=lambda: ValidationResultsModel(), description="Validation results")

    model_config = ConfigDict(extra='forbid')

    def update_state(self, **kwargs):
        """Update state attributes"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

class MetricCache(BaseModel):
    """Unified cache for all metric types"""
    data: CacheDataModel = Field(default_factory=CacheDataModel, description="Cache data model")

    model_config = ConfigDict(extra='forbid')
    
class MetricCacheConfig(BaseModel):
    """Consolidated cache configuration"""
    foundational: MetricCache = Field(default_factory=MetricCache)
    flow_metrics: MetricCache = Field(default_factory=MetricCache)
    adaptive: MetricCache = Field(default_factory=MetricCache)
    heatmap: MetricCache = Field(default_factory=MetricCache)
    normalization: MetricCache = Field(default_factory=MetricCache)

    model_config = ConfigDict(extra='forbid')

    def get_cache(self, metric_name: str) -> CacheDataModel:
        """Get cache for specific metric category - STRICT PYDANTIC V2"""
        if hasattr(self, metric_name):
            return getattr(self, metric_name).data
        return CacheDataModel()

    def set_cache(self, metric_name: str, data: CacheDataModel):
        """Set cache for specific metric category - STRICT PYDANTIC V2"""
        if hasattr(self, metric_name):
            cache = getattr(self, metric_name)
            cache.data = data

class CoreCalculator:
    """
    Consolidated core calculator with base utilities and foundational metrics.
    
    Combines functionality from:
    - BaseCalculator: Core utilities, caching, validation
    - FoundationalMetricsCalculator: Tier 1 metrics
    """
    
    def __init__(self, config_manager: Any, historical_data_manager: Any, enhanced_cache_manager: EnhancedCacheManagerV2_5):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config_manager = config_manager
        self.historical_data_manager = historical_data_manager
        self.enhanced_cache_manager = enhanced_cache_manager
        
        # Initialize unified state and cache
        self._calculation_state = MetricCalculationState(
            current_symbol=None,
            calculation_timestamp=None
        )
        self._metric_cache_config = MetricCacheConfig()
        self._options_df: Optional[pd.DataFrame] = None  # Always present, set by process_data_bundle_v2
        
        # Configuration constants
        self.METRIC_BOUNDS = {
            'gib_oi_based_und': (-1000000, 1000000),
            'hp_eod_und': (-100, 100),
            'net_cust_delta_flow_und': (-1000000, 1000000),
            'net_cust_gamma_flow_und': (-1000000, 1000000),
            'net_cust_vega_flow_und': (-1000000, 1000000),
            'net_cust_theta_flow_und': (-1000000, 1000000)
        }
    
    # =============================================================================
    # FOUNDATIONAL METRICS (Tier 1) - Consolidated from foundational_metrics.py
    # =============================================================================
    
    def calculate_all_foundational_metrics(self, raw_und_data: RawUnderlyingDataCombinedV2_5) -> FoundationalMetricsOutput:
        """
        Calculate all foundational metrics and return them as a FoundationalMetricsOutput model.
        """
        metrics = {
            "symbol": raw_und_data.symbol,
            "price": raw_und_data.price,
            "timestamp": raw_und_data.timestamp,
            "price_change_abs_und": raw_und_data.price_change_abs_und,
            "price_change_pct_und": raw_und_data.price_change_pct_und,
            "day_open_price_und": raw_und_data.day_open_price_und,
            "day_high_price_und": raw_und_data.day_high_price_und,
            "day_low_price_und": raw_und_data.day_low_price_und,
            "prev_day_close_price_und": raw_und_data.prev_day_close_price_und,
            "u_volatility": raw_und_data.u_volatility,
            "day_volume": raw_und_data.day_volume,
            "call_gxoi": raw_und_data.call_gxoi,
            "put_gxoi": raw_und_data.put_gxoi,
            "gammas_call_buy": raw_und_data.gammas_call_buy,
            "gammas_call_sell": raw_und_data.gammas_call_sell,
            "gammas_put_buy": raw_und_data.gammas_put_buy,
            "gammas_put_sell": raw_und_data.gammas_put_sell,
            "deltas_call_buy": raw_und_data.deltas_call_buy,
            "deltas_call_sell": raw_und_data.deltas_call_sell,
            "deltas_put_buy": raw_und_data.deltas_put_buy,
            "deltas_put_sell": raw_und_data.deltas_put_sell,
            "vegas_call_buy": raw_und_data.vegas_call_buy,
            "vegas_call_sell": raw_und_data.vegas_call_sell,
            "vegas_put_buy": raw_und_data.vegas_put_buy,
            "vegas_put_sell": raw_und_data.vegas_put_sell,
            "thetas_call_buy": raw_und_data.thetas_call_buy,
            "thetas_call_sell": raw_und_data.thetas_call_sell,
            "thetas_put_buy": raw_und_data.thetas_put_buy,
            "thetas_put_sell": raw_und_data.thetas_put_sell,
            "call_vxoi": raw_und_data.call_vxoi,
            "put_vxoi": raw_und_data.put_vxoi,
            "value_bs": raw_und_data.value_bs,
            "volm_bs": raw_und_data.volm_bs,
            "deltas_buy": raw_und_data.deltas_buy,
            "deltas_sell": raw_und_data.deltas_sell,
            "vegas_buy": raw_und_data.vegas_buy,
            "vegas_sell": raw_und_data.vegas_sell,
            "thetas_buy": raw_und_data.thetas_buy,
            "thetas_sell": raw_und_data.thetas_sell,
            "volm_call_buy": raw_und_data.volm_call_buy,
            "volm_put_buy": raw_und_data.volm_put_buy,
            "volm_call_sell": raw_und_data.volm_call_sell,
            "volm_put_sell": raw_und_data.volm_put_sell,
            "value_call_buy": raw_und_data.value_call_buy,
            "value_put_buy": raw_und_data.value_put_buy,
            "value_call_sell": raw_und_data.value_call_sell,
            "value_put_sell": raw_und_data.value_put_sell,
            "vflowratio": raw_und_data.vflowratio,
            "dxoi": raw_und_data.dxoi,
            "gxoi": raw_und_data.gxoi,
            "vxoi": raw_und_data.vxoi,
            "txoi": raw_und_data.txoi,
            "call_dxoi": raw_und_data.call_dxoi,
            "put_dxoi": raw_und_data.put_dxoi,
            "tradier_iv5_approx_smv_avg": raw_und_data.tradier_iv5_approx_smv_avg,
            "total_call_oi_und": raw_und_data.total_call_oi_und,
            "total_put_oi_und": raw_und_data.total_put_oi_und,
            "total_call_vol_und": raw_und_data.total_call_vol_und,
            "total_put_vol_und": raw_und_data.total_put_vol_und,
            "tradier_open": raw_und_data.tradier_open,
            "tradier_high": raw_und_data.tradier_high,
            "tradier_low": raw_und_data.tradier_low,
            "tradier_close": raw_und_data.tradier_close,
            "tradier_volume": raw_und_data.tradier_volume,
            "tradier_vwap": raw_und_data.tradier_vwap,
        }

        # Calculate Net Customer Greek Flows first
        greek_flows = self._calculate_net_customer_greek_flows_v2_5(raw_und_data)
        metrics.update(greek_flows)

        # Calculate GIB, HP_EOD, TD_GIB (now with access to calculated greek flows)
        gib_metrics = self._calculate_gib_based_metrics_v2_5(raw_und_data, greek_flows)
        metrics.update(gib_metrics)

        # Validate and return the FoundationalMetricsOutput model
        foundational_output = FoundationalMetricsOutput(**metrics)
        self._validate_foundational_metrics_v2_5(foundational_output)
        return foundational_output

    def _validate_foundational_metrics_v2_5(self, model: FoundationalMetricsOutput):
        """STRICT PYDANTIC V2-ONLY: Validate foundational metrics directly on model"""
        validation_errors = []

        # Check for reasonable bounds - FAIL FAST if values are suspicious
        if model.gib_oi_based_und is not None and abs(model.gib_oi_based_und) > 1000000:
            validation_errors.append(f"GIB value out of bounds: {model.gib_oi_based_und}")

        if model.hp_eod_und is not None and abs(model.hp_eod_und) > 100:
            validation_errors.append(f"HP_EOD value out of bounds: {model.hp_eod_und}")

        # FAIL FAST on validation errors - NO SILENT FAILURES
        if validation_errors:
            self.logger.error(f"CRITICAL: Foundational metrics validation failed: {validation_errors}")
            raise ValueError(f"CRITICAL: Foundational metrics validation failed - {validation_errors}")

        self.logger.debug("Foundational metrics validation passed")

    def _calculate_net_customer_greek_flows_v2_5(self, raw_und_data: RawUnderlyingDataCombinedV2_5) -> Dict[str, float]:
        """Calculate Net Customer Greek Flows and return as a dictionary"""
        self.logger.debug("Calculating Net Customer Greek Flows...")
        
        # Delta Flow - STRICT PYDANTIC V2: Direct model access, FAIL FAST on missing data
        deltas_buy = self._require_pydantic_field(raw_und_data, 'deltas_buy', 'deltas buy data')
        deltas_sell = self._require_pydantic_field(raw_und_data, 'deltas_sell', 'deltas sell data')
        net_cust_delta_flow_und = deltas_buy - deltas_sell

        # Gamma Flow - FAIL FAST on missing data
        gamma_buy = (self._require_pydantic_field(raw_und_data, 'gammas_call_buy', 'gamma call buy') +
                    self._require_pydantic_field(raw_und_data, 'gammas_put_buy', 'gamma put buy'))
        gamma_sell = (self._require_pydantic_field(raw_und_data, 'gammas_call_sell', 'gamma call sell') +
                     self._require_pydantic_field(raw_und_data, 'gammas_put_sell', 'gamma put sell'))
        net_cust_gamma_flow_und = gamma_buy - gamma_sell

        # Vega Flow - FAIL FAST on missing data
        vegas_buy = self._require_pydantic_field(raw_und_data, 'vegas_buy', 'vegas buy data')
        vegas_sell = self._require_pydantic_field(raw_und_data, 'vegas_sell', 'vegas sell data')
        net_cust_vega_flow_und = vegas_buy - vegas_sell

        # Theta Flow - FAIL FAST on missing data
        thetas_buy = self._require_pydantic_field(raw_und_data, 'thetas_buy', 'thetas buy data')
        thetas_sell = self._require_pydantic_field(raw_und_data, 'thetas_sell', 'thetas sell data')
        net_cust_theta_flow_und = thetas_buy - thetas_sell

        self.logger.debug("Net Customer Greek Flows calculated.")
        return {
            'net_cust_delta_flow_und': net_cust_delta_flow_und,
            'net_cust_gamma_flow_und': net_cust_gamma_flow_und,
            'net_cust_vega_flow_und': net_cust_vega_flow_und,
            'net_cust_theta_flow_und': net_cust_theta_flow_und
        }

    def _calculate_gib_based_metrics_v2_5(self, raw_und_data: RawUnderlyingDataCombinedV2_5, net_customer_flows: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """Calculate GIB, HP_EOD, and TD_GIB metrics and return as a dictionary"""
        self.logger.debug("Calculating GIB-based metrics...")

        # GIB (Gamma Imbalance from Open Interest) - FAIL FAST on missing data
        call_gxoi = self._require_pydantic_field(raw_und_data, 'call_gxoi', 'call gamma exposure')
        put_gxoi = self._require_pydantic_field(raw_und_data, 'put_gxoi', 'put gamma exposure')
        gib_raw_gamma_units = put_gxoi - call_gxoi

        # Calculate dollar value - FAIL FAST on missing price
        underlying_price = self._require_pydantic_field(raw_und_data, 'price', 'underlying price')
        if underlying_price <= 0:
            raise ValueError(f"CRITICAL: Invalid underlying price {underlying_price} - must be positive!")

        contract_multiplier = 100
        gib_dollar_value_full = gib_raw_gamma_units * underlying_price * contract_multiplier

        # Scale for display (optimized scaling)
        gib_display_value = gib_dollar_value_full / 10000.0

        # HP_EOD (End-of-Day Hedging Pressure) - pass calculated gamma flow if available
        net_gamma_flow = net_customer_flows.get('net_cust_gamma_flow_und') if net_customer_flows else None
        hp_eod_value = self._calculate_hp_eod_optimized_v2_5(raw_und_data, gib_display_value, net_gamma_flow)

        # TD_GIB (Traded Dealer Gamma Imbalance) - pass calculated gamma flow if available
        td_gib_value = self._calculate_td_gib_optimized_v2_5(raw_und_data, net_gamma_flow)

        self.logger.debug(f"GIB metrics calculated: GIB={gib_display_value:.2f}, HP_EOD={hp_eod_value:.2f}, TD_GIB={td_gib_value:.2f}")
        return {
            'gib_oi_based_und': gib_display_value,
            'gib_raw_gamma_units_und': gib_raw_gamma_units,
            'gib_dollar_value_full_und': gib_dollar_value_full,
            'hp_eod_und': hp_eod_value,
            'td_gib_und': td_gib_value
        }
    
    def _calculate_hp_eod_optimized_v2_5(self, raw_und_data: RawUnderlyingDataCombinedV2_5, gib_value: float, net_cust_gamma_flow: Optional[float] = None) -> float:
        """HP_EOD calculation - now accepts calculated gamma flow as parameter"""
        try:
            current_time = datetime.now().time()
            market_close = datetime.strptime("16:00", "%H:%M").time()

            # Time-based scaling factor
            if current_time >= market_close:
                time_scaling = 1.0
            else:
                hours_to_close = (datetime.combine(date.today(), market_close) -
                                datetime.combine(date.today(), current_time)).seconds / 3600
                time_scaling = max(0.1, 1.0 - (hours_to_close / 6.5))

            # Calculate pressure components
            gib_component = gib_value * 0.6
            
            # Use provided gamma flow or try to calculate it from raw data
            if net_cust_gamma_flow is not None:
                gamma_flow = net_cust_gamma_flow
            else:
                # Try to calculate from raw data components
                try:
                    gamma_buy = (self._require_pydantic_field(raw_und_data, 'gammas_call_buy', 'gamma call buy') +
                               self._require_pydantic_field(raw_und_data, 'gammas_put_buy', 'gamma put buy'))
                    gamma_sell = (self._require_pydantic_field(raw_und_data, 'gammas_call_sell', 'gamma call sell') +
                                self._require_pydantic_field(raw_und_data, 'gammas_put_sell', 'gamma put sell'))
                    gamma_flow = gamma_buy - gamma_sell
                except Exception:
                    # If we can't calculate it, use a simplified approach based on GIB
                    self.logger.warning("Could not calculate net customer gamma flow, using simplified HP_EOD calculation")
                    gamma_flow = gib_value * 0.1  # Simplified fallback
            
            flow_component = float(gamma_flow) * 0.4 / 10000.0

            hp_eod = (gib_component + flow_component) * time_scaling
            return self._bound_value(hp_eod, -100, 100)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating HP_EOD: {e}")
            raise ValueError(f"CRITICAL: HP_EOD calculation failed - cannot return fake 0.0 value! Error: {e}") from e

    def _calculate_td_gib_optimized_v2_5(self, raw_und_data: RawUnderlyingDataCombinedV2_5, net_cust_gamma_flow: Optional[float] = None) -> float:
        """TD_GIB calculation - now accepts calculated gamma flow as parameter"""
        try:
            # Use provided gamma flow or try to calculate it
            if net_cust_gamma_flow is not None:
                gamma_flow = float(net_cust_gamma_flow)
            else:
                # Try to calculate from raw data components
                try:
                    gamma_buy = (self._require_pydantic_field(raw_und_data, 'gammas_call_buy', 'gamma call buy') +
                               self._require_pydantic_field(raw_und_data, 'gammas_put_buy', 'gamma put buy'))
                    gamma_sell = (self._require_pydantic_field(raw_und_data, 'gammas_call_sell', 'gamma call sell') +
                                self._require_pydantic_field(raw_und_data, 'gammas_put_sell', 'gamma put sell'))
                    gamma_flow = float(gamma_buy - gamma_sell)
                except Exception:
                    # If we can't calculate it, use a simplified approach
                    self.logger.warning("Could not calculate net customer gamma flow for TD_GIB, using simplified calculation")
                    gamma_flow = 0.0  # Simplified fallback

            day_volume = self._require_pydantic_field(raw_und_data, 'day_volume', 'daily volume')
            day_volume = float(day_volume)

            volume_factor = max(1.0, day_volume / 1000000)

            td_gib = gamma_flow * volume_factor / 10000.0
            return self._bound_value(td_gib, -1000, 1000)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating TD_GIB: {e}")
            raise ValueError(f"CRITICAL: TD_GIB calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    # =============================================================================
    # CORE UTILITIES - Consolidated and optimized from base_calculator.py
    # =============================================================================
    
    def _require_pydantic_field(self, pydantic_model, field_name: str, field_description: str):
        """FAIL-FAST: Require field from Pydantic model - NO DICTIONARY CONVERSION ALLOWED"""
        if not hasattr(pydantic_model, field_name):
            raise ValueError(f"CRITICAL: Required field '{field_name}' ({field_description}) missing from Pydantic model!")

        value = getattr(pydantic_model, field_name)
        if value is None:
            raise ValueError(f"CRITICAL: Field '{field_name}' ({field_description}) is None - cannot use fake defaults in financial calculations!")

        return value


    
    def _bound_value(self, value: float, min_val: float, max_val: float) -> float:
        """Bound value within specified range"""
        return max(min_val, min(max_val, value))
    
    def _validate_foundational_metrics(self, und_data: Dict) -> bool:
        """Validate foundational metrics against bounds"""
        validation_results = {}
        
        for metric, bounds in self.METRIC_BOUNDS.items():
            if metric in und_data:
                value = und_data[metric]
                min_val, max_val = bounds
                is_valid = min_val <= value <= max_val
                validation_results[metric] = {
                    'value': value,
                    'valid': is_valid,
                    'bounds': bounds
                }
                
                if not is_valid:
                    self.logger.warning(f"Metric {metric} value {value} outside bounds {bounds}")
        
        self._calculation_state.validation_results.update(validation_results)
        return all(result['valid'] for result in validation_results.values())
    
    def _fail_fast_on_missing_metrics(self, error_context: str) -> None:
        """FAIL FAST - NO DEFAULT FOUNDATIONAL METRICS ALLOWED!"""
        raise ValueError(
            f"CRITICAL: Failed to calculate foundational metrics - {error_context}. "
            "NO FAKE DATA WILL BE SUBSTITUTED! Fix the underlying calculation issue."
        )
    
    # =============================================================================
    # CACHING UTILITIES - Unified and optimized
    # =============================================================================
    
    def _add_to_intraday_cache(self, symbol: str, metric_name: str, value: float, max_size: int = 200) -> List[float]:
        """Unified intraday caching with automatic cleanup"""
        try:
            # CRITICAL FIX: Check if cache manager is None
            if self.enhanced_cache_manager is None:
                return [float(value)]

            # Get existing cache or create new
            # CRITICAL FIX: Use correct cache manager signature (symbol, metric_name)
            cache_data = self.enhanced_cache_manager.get(symbol, f"{metric_name}_intraday")
            if cache_data is None:
                cache_data = deque(maxlen=max_size)
            elif not isinstance(cache_data, deque):
                cache_data = deque(cache_data if isinstance(cache_data, list) else [cache_data], maxlen=max_size)
            
            # Add new value
            cache_data.append(float(value))
            
            # Store back in cache
            # CRITICAL FIX: Use correct cache manager method (put) and signature
            self.enhanced_cache_manager.put(symbol, f"{metric_name}_intraday", list(cache_data), cache_level=CacheLevel.MEMORY)
            
            return list(cache_data)
            
        except Exception as e:
            self.logger.warning(f"Error updating intraday cache for {symbol}_{metric_name}: {e}")
            import traceback
            self.logger.warning(f"Cache error traceback: {traceback.format_exc()}")
            return [float(value)]
    
    def _calculate_percentile_gauge_value(self, cache_data: List[float], current_value: float) -> float:
        """FAIL-FAST: Calculate percentile-based gauge value - NO FAKE DEFAULTS ALLOWED"""
        if not cache_data or len(cache_data) < 2:
            raise ValueError(f"CRITICAL: Insufficient cache data for percentile gauge calculation - need at least 2 data points, got {len(cache_data) if cache_data else 0}!")
        
        try:
            # Use percentile ranking for small datasets
            sorted_data = sorted(cache_data)
            rank = sum(1 for x in sorted_data if x <= current_value)
            percentile = rank / len(sorted_data)
            
            # Convert to z-score-like value
            return (percentile - 0.5) * 4  # Scale to approximate z-score range
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating percentile gauge: {e}")
            raise ValueError(f"CRITICAL: Percentile gauge calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    def sanitize_symbol(self, symbol: str) -> str:
        """Sanitize ticker symbol for consistent processing"""
        if not symbol:
            return "UNKNOWN"
        return str(symbol).upper().strip()
    
    def _is_futures_symbol(self, symbol: str) -> bool:
        """Determine if symbol is a futures contract"""
        futures_patterns = ['/ES', '/NQ', '/YM', '/RTY', '/CL', '/GC', '/SI']
        return any(pattern in symbol.upper() for pattern in futures_patterns)

# Export the consolidated calculator
__all__ = ['CoreCalculator', 'MetricCalculationState', 'MetricCache', 'MetricCacheConfig']
