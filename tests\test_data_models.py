"""
Comprehensive tests for EOTS v2.5 data models.

Tests cover:
- Model validation
- Field constraints
- Business logic validation
- Serialization/deserialization
- Backward compatibility
"""

import pytest
from datetime import datetime, timezone, timedelta
from typing import Dict, Any
import uuid

# Import all data models
from data_models.core_models import (
    RawOptionsContractV2_5,
    RawUnderlyingDataV2_5,
    ProcessedContractMetricsV2_5,
    ProcessedStrikeLevelMetricsV2_5,
    ProcessedUnderlyingAggregatesV2_5,
    ProcessedDataBundleV2_5,
    FinalAnalysisBundleV2_5,
    UnprocessedDataBundleV2_5,
    RawUnderlyingDataCombinedV2_5
)

from data_models.trading_market_models import (
    MarketRegimeState,
    SignalPayloadV2_5,
    KeyLevelV2_5,
    KeyLevelsDataV2_5,
    DynamicThresholdsV2_5,
    TickerContextDictV2_5,
    TradeParametersV2_5,
    ActiveRecommendationPayloadV2_5
)

from data_models.ai_ml_models import (
    HuiHuiExpertType,
    MOEExpertRegistryV2_5,
    MOEGatingNetworkV2_5,
    HuiHuiModelConfigV2_5,
    PerformanceMetricV2_5,
    SystemResourceSnapshotV2_5
)

from data_models.dashboard_ui_models import (
    DashboardModeType,
    ChartType,
    DashboardConfigV2_5,
    ComponentComplianceV2_5,
    DashboardStateV2_5
)

from data_models.configuration_models import (
    EOTSConfigV2_5
)


class TestRawOptionsContractV2_5:
    """Test suite for RawOptionsContractV2_5 model."""
    
    def test_valid_contract_creation(self):
        """Test creating a valid options contract."""
        contract = RawOptionsContractV2_5(
            contract_symbol="SPY240315C00500000",
            strike=500.0,
            opt_kind="call",
            dte_calc=30.0,
            iv=0.25,
            delta_contract=0.5,
            gamma_contract=0.01,
            vega_contract=0.1
        )
        assert contract.contract_symbol == "SPY240315C00500000"
        assert contract.strike == 500.0
        assert contract.opt_kind == "call"
        assert contract.dte_calc == 30.0
    
    def test_invalid_strike_price(self):
        """Test that negative strike prices are rejected."""
        with pytest.raises(ValueError, match="Strike price must be positive"):
            RawOptionsContractV2_5(
                contract_symbol="SPY240315C00500000",
                strike=-500.0,
                opt_kind="call",
                dte_calc=30.0
            )
    
    def test_invalid_option_type(self):
        """Test that invalid option types are rejected."""
        with pytest.raises(ValueError, match="opt_kind must be either 'call' or 'put'"):
            RawOptionsContractV2_5(
                contract_symbol="SPY240315C00500000",
                strike=500.0,
                opt_kind="invalid",
                dte_calc=30.0
            )
    
    def test_invalid_implied_volatility(self):
        """Test that invalid implied volatility values are rejected."""
        with pytest.raises(ValueError, match="Implied volatility must be between 0.0 and 10.0"):
            RawOptionsContractV2_5(
                contract_symbol="SPY240315C00500000",
                strike=500.0,
                opt_kind="call",
                dte_calc=30.0,
                iv=15.0  # Too high
            )
    
    def test_invalid_delta(self):
        """Test that invalid delta values are rejected."""
        with pytest.raises(ValueError, match="Delta must be between -1.0 and 1.0"):
            RawOptionsContractV2_5(
                contract_symbol="SPY240315C00500000",
                strike=500.0,
                opt_kind="call",
                dte_calc=30.0,
                delta_contract=2.0  # Too high
            )


class TestSignalPayloadV2_5:
    """Test suite for SignalPayloadV2_5 model."""
    
    def test_valid_signal_creation(self):
        """Test creating a valid signal payload."""
        signal = SignalPayloadV2_5(
            signal_id=str(uuid.uuid4()),
            signal_name="VAPI_FA_Bullish_Surge",
            symbol="SPY",
            timestamp=datetime.now(timezone.utc),
            signal_type="Directional",
            direction="Bullish",
            strength_score=2.5,
            strike_impacted=500.0,
            regime_at_signal_generation="BULLISH_TREND"
        )
        assert signal.signal_name == "VAPI_FA_Bullish_Surge"
        assert signal.direction == "Bullish"
        assert signal.strength_score == 2.5
    
    def test_invalid_direction(self):
        """Test that invalid direction values are rejected."""
        with pytest.raises(ValueError, match="direction must be 'Bullish', 'Bearish', or 'Neutral'"):
            SignalPayloadV2_5(
                signal_id=str(uuid.uuid4()),
                signal_name="Test Signal",
                symbol="SPY",
                timestamp=datetime.now(timezone.utc),
                signal_type="Directional",
                direction="Invalid",
                strength_score=1.0
            )
    
    def test_invalid_signal_type(self):
        """Test that invalid signal types are rejected."""
        with pytest.raises(ValueError, match="signal_type must be one of"):
            SignalPayloadV2_5(
                signal_id=str(uuid.uuid4()),
                signal_name="Test Signal",
                symbol="SPY",
                timestamp=datetime.now(timezone.utc),
                signal_type="Invalid",
                strength_score=1.0
            )
    
    def test_strength_score_bounds(self):
        """Test that strength score is within bounds."""
        with pytest.raises(ValueError):
            SignalPayloadV2_5(
                signal_id=str(uuid.uuid4()),
                signal_name="Test Signal",
                symbol="SPY",
                timestamp=datetime.now(timezone.utc),
                signal_type="Directional",
                strength_score=10.0  # Too high
            )


class TestKeyLevelV2_5:
    """Test suite for KeyLevelV2_5 model."""
    
    def test_valid_key_level_creation(self):
        """Test creating a valid key level."""
        level = KeyLevelV2_5(
            level_price=500.0,
            level_type="Support",
            conviction_score=0.85,
            contributing_metrics=["A-MSPI", "NVP_Peak"],
            source_identifier="A-MSPI_Daily"
        )
        assert level.level_price == 500.0
        assert level.level_type == "Support"
        assert level.conviction_score == 0.85
    
    def test_invalid_level_type(self):
        """Test that invalid level types are rejected."""
        with pytest.raises(ValueError, match="level_type must be one of"):
            KeyLevelV2_5(
                level_price=500.0,
                level_type="Invalid",
                conviction_score=0.85
            )
    
    def test_conviction_score_bounds(self):
        """Test that conviction score is within bounds."""
        with pytest.raises(ValueError):
            KeyLevelV2_5(
                level_price=500.0,
                level_type="Support",
                conviction_score=10.0  # Too high
            )


class TestDynamicThresholdsV2_5:
    """Test suite for DynamicThresholdsV2_5 model."""
    
    def test_valid_thresholds_creation(self):
        """Test creating valid dynamic thresholds."""
        thresholds = DynamicThresholdsV2_5(
            vapi_fa_bullish_thresh=1.5,
            vapi_fa_bearish_thresh=-1.5,
            vri_bullish_thresh=0.6,
            vri_bearish_thresh=-0.6
        )
        assert thresholds.vapi_fa_bullish_thresh == 1.5
        assert thresholds.vri_bullish_thresh == 0.6
    
    def test_vri_threshold_validation(self):
        """Test VRI threshold validation."""
        with pytest.raises(ValueError, match="vri_bullish_thresh must be between 0 and 1"):
            DynamicThresholdsV2_5(vri_bullish_thresh=1.5)
        
        with pytest.raises(ValueError, match="vri_bearish_thresh must be between -1 and 0"):
            DynamicThresholdsV2_5(vri_bearish_thresh=0.5)


class TestMarketRegimeState:
    """Test suite for MarketRegimeState enum."""
    
    def test_enum_values(self):
        """Test that all expected enum values exist."""
        expected_values = [
            "BULLISH_TREND", "BEARISH_TREND", "SIDEWAYS",
            "VOLATILITY_EXPANSION", "VOLATILITY_CONTRACTION",
            "BULLISH_REVERSAL", "BEARISH_REVERSAL",
            "DISTRIBUTION", "ACCUMULATION", "CAPITULATION",
            "EUPHORIA", "PANIC", "CONSOLIDATION",
            "BREAKOUT", "BREAKDOWN", "CHOPPY",
            "TRENDING", "RANGE_BOUND", "UNDEFINED"
        ]
        
        for value in expected_values:
            assert hasattr(MarketRegimeState, value)
            assert MarketRegimeState[value].value == value.lower()


class TestHuiHuiExpertType:
    """Test suite for HuiHuiExpertType enum."""
    
    def test_enum_values(self):
        """Test that all expected expert types exist."""
        expected_types = [
            "MARKET_REGIME", "OPTIONS_FLOW", "SENTIMENT", 
            "ORCHESTRATOR", "VOLATILITY", "LIQUIDITY", 
            "RISK", "EXECUTION"
        ]
        
        for expert_type in expected_types:
            assert hasattr(HuiHuiExpertType, expert_type)


class TestModelSerialization:
    """Test model serialization and deserialization."""
    
    def test_signal_payload_serialization(self):
        """Test SignalPayloadV2_5 serialization."""
        signal = SignalPayloadV2_5(
            signal_id=str(uuid.uuid4()),
            signal_name="Test Signal",
            symbol="SPY",
            timestamp=datetime.now(timezone.utc),
            signal_type="Directional",
            direction="Bullish",
            strength_score=1.5
        )
        
        # Test JSON serialization
        json_data = signal.model_dump()
        assert isinstance(json_data, dict)
        assert json_data["signal_name"] == "Test Signal"
        
        # Test deserialization
        reconstructed = SignalPayloadV2_5.model_validate(json_data)
        assert reconstructed.signal_name == signal.signal_name
        assert reconstructed.strength_score == signal.strength_score


if __name__ == "__main__":
    pytest.main([__file__])
