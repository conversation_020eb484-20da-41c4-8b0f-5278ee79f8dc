---
sidebar_position: 1
---

import YouTubeVideoEmbed from '@site/src/components/HomepageFeatures/YouTubeVideoEmbed';

# MCP Database Server

The **MCP Database Server** is a powerful solution that enables <PERSON> to interact with databases using Model Context Protocol (MCP).

With the MCP Database Server, you can:
	- Connect <PERSON> to SQLite, SQL Server, and PostgreSQL databases
	- Execute SQL queries and view results directly in your conversations
	- Perform database operations like creating tables, inserting data, and modifying schemas
	- Export query results as CSV or JSON
	- Track business insights discovered during database analysis

![MCP Database Server](./img/mcp-server.gif)

:::tip What's New
Check our [Release Notes](./release-notes) to see what's new in version 1.0.2!
:::

## Installation

You can install the MCP Database Server package using NPM:

```bash
npm install -g @executeautomation/database-server
```

### Configuring MCP Database Server in Claude Desktop

Here's the Claude Desktop configuration to use the MCP Database Server:

Modify your `claude-desktop-config.json` file as shown below:

```json
{
  "mcpServers": {
    "sqlite": {
      "command": "npx",
      "args": [
        "-y",
        "@executeautomation/database-server",
        "/path/to/your/database.db"
      ]
    },
    "sqlserver": {
      "command": "npx",
      "args": [
        "-y",
        "@executeautomation/database-server",
        "--sqlserver",
        "--server", "your-server-name",
        "--database", "your-database-name",
        "--user", "your-username",
        "--password", "your-password"
      ]
    },
    "postgresql": {
      "command": "npx",
      "args": [
        "-y",
        "@executeautomation/database-server",
        "--postgresql",
        "--host", "your-host-name",
        "--database", "your-database-name",
        "--user", "your-username",
        "--password", "your-password"
      ]
    }
  }
}
```

The Claude Desktop configuration file is typically located at:
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- Linux: `~/.config/Claude/claude_desktop_config.json`

## What is Model Context Protocol (MCP)?

MCP allows Claude to interact with external tools and systems, including databases. This video provides a high-level overview of what Claude's MCP is and how helpful it can be for AI agents:

<YouTubeVideoEmbed videoId="hGJQMbpsTi4" />
