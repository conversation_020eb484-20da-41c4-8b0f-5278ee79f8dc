---
description: when specific said files are mentioned, the agent should call on the `context7 mcp`
globs: 
alwaysApply: false
---
### **Systemic Rule: A Curated Tool-belt Governed by `context7 mcp`**

**Core Purpose:** *To ensure all solutions are built using approved, standardized, and well-understood tools by making the `context7 mcp` knowledge base the single source of truth for all library and dependency information. This guarantees that your recommendations align with established team practices.*

**The Primary Directive:** *Your foremost directive when using any external library or tool is to **first consult the `context7 mcp` knowledge base.** This system is the designated source of truth for approved libraries, standard configurations, security policies, and versioning. Before proposing a solution using any library—including those listed below—you MUST query `context7 mcp` to ensure your recommendations align with its established patterns and best practices.*

---

#### **Core Knowledge Domains for `context7 mcp`**

*The following libraries represent key domains that should have comprehensive documentation and best-practice guides within the `context7 mcp`. Your role is to use `context7 mcp`'s knowledge on these topics to inform and validate your solutions.*

#### For Building Web APIs & Services

* **FastAPI:**
    * **`context7 mcp` Goal:** Should contain standard patterns for project structure, dependency injection, logging, and error handling in FastAPI.
    * **Enhanced Capability:** You can scaffold entire API endpoints that conform to the pre-approved structure defined in `context7 mcp`.

* **SQLAlchemy (2.0+):**
    * **`context7 mcp` Goal:** Should define the standard way to configure database connections, structure data models, and manage sessions for the team's chosen database.
    * **Enhanced Capability:** You can write efficient, non-blocking database queries that use the exact session management and model conventions specified in `context7 mcp`.

* **SQLModel:**
    * **`context7 mcp` Goal:** If used, `context7 mcp` should specify it as the preferred ORM and provide examples of creating unified Pydantic/SQLAlchemy models.
    * **Enhanced Capability:** You can generate single, DRY data models that are guaranteed to be compliant with both API validation and database interaction standards.

#### For Building Command-Line Interfaces (CLIs)

* **Typer:**
    * **`context7 mcp` Goal:** Should provide templates for creating new CLI applications, including standards for argument naming and help text.
    * **Enhanced Capability:** You can instantly turn a Python function into a CLI application that matches the look, feel, and structure of other internal tools.

#### For High-Performance Data Manipulation

* **Polars:**
    * **`context7 mcp` Goal:** Should contain best practices for using Polars for common data engineering tasks, including when to prefer it over Pandas.
    * **Enhanced Capability:** When appropriate, you can propose and implement high-performance Polars solutions that follow vetted team patterns.

* **Pandas:**
    * **`context7 mcp` Goal:** Should document optimized patterns for common operations and explicitly warn against known anti-patterns (`iterrows`, etc.).
    * **Enhanced Capability:** You can refactor inefficient Pandas code into optimized, vectorized operations that align with the documented best practices.

#### For Asynchronous Task Queues & Background Jobs

* **Celery / Arq:**
    * **`context7 mcp` Goal:** Should designate the company's standard task queue library (e.g., Celery) and provide a template for worker configuration, task definition, and retry policies.
    * **Enhanced Capability:** You can define background tasks and configure workers using the exact broker settings and retry logic approved in `context7 mcp`.

#### For Modern Observability

* **OpenTelemetry Python SDK:**
    * **`context7 mcp` Goal:** Should define the standard process for instrumenting new services, including which exporters to use and what default attributes to attach to traces and metrics.
    * **Enhanced Capability:** You can automatically instrument new applications in a way that is guaranteed to integrate seamlessly with the organization's existing monitoring platforms.