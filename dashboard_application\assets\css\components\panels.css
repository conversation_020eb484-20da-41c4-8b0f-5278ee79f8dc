/* =============================================================================
   ELITE OPTIONS TRADING SYSTEM - PANEL COMPONENTS
   Core panel system based on custom dashboard analysis
============================================================================= */

/* ==========================================================================
   BASE PANEL SYSTEM
========================================================================== */

.panel-base {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);
}

.panel-elevated {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-elevated);
  z-index: var(--z-dropdown);
}

.panel-floating {
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-accent);
  box-shadow: var(--shadow-strong);
  backdrop-filter: var(--backdrop-blur-md);
  z-index: var(--z-modal);
}

/* ==========================================================================
   PANEL VARIANTS
========================================================================== */

.panel-glass {
  background-color: rgba(26, 26, 26, 0.8);
  backdrop-filter: var(--backdrop-blur-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-soft);
}

.panel-solid {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  box-shadow: none;
}

.panel-bordered {
  background-color: transparent;
  border: 2px solid var(--border-accent);
  box-shadow: none;
}

.panel-gradient {
  background: var(--gradient-dark);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-soft);
}

/* ==========================================================================
   PANEL SIZES
========================================================================== */

.panel-compact {
  padding: var(--space-md);
}

.panel-comfortable {
  padding: var(--space-lg);
}

.panel-spacious {
  padding: var(--space-xl);
}

.panel-minimal {
  padding: var(--space-sm);
}

/* ==========================================================================
   PANEL STATES
========================================================================== */

.panel-hover:hover {
  border-color: var(--border-accent);
  box-shadow: var(--shadow-elevated);
  transform: translateY(-1px);
}

.panel-active {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
  background-color: var(--bg-tertiary);
}

.panel-selected {
  border-color: var(--accent-primary);
  background-color: var(--accent-primary-alpha);
  box-shadow: var(--shadow-glow);
}

.panel-disabled {
  opacity: 0.6;
  pointer-events: none;
  filter: grayscale(0.3);
}

.panel-loading {
  position: relative;
  pointer-events: none;
}

.panel-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(10, 10, 10, 0.7);
  backdrop-filter: var(--backdrop-blur-sm);
  z-index: 1;
}

.panel-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  border: 2px solid var(--border-primary);
  border-top-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
}

/* ==========================================================================
   PANEL HEADERS
========================================================================== */

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-secondary);
}

.panel-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.panel-subtitle {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: 0;
  margin-top: var(--space-xs);
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.panel-icon {
  width: 20px;
  height: 20px;
  color: var(--accent-primary);
  flex-shrink: 0;
}

/* ==========================================================================
   PANEL CONTENT AREAS
========================================================================== */

.panel-content {
  position: relative;
}

.panel-body {
  margin-bottom: var(--space-lg);
}

.panel-body:last-child {
  margin-bottom: 0;
}

.panel-footer {
  margin-top: var(--space-lg);
  padding-top: var(--space-md);
  border-top: 1px solid var(--border-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-md);
}

.panel-section {
  margin-bottom: var(--space-xl);
}

.panel-section:last-child {
  margin-bottom: 0;
}

.panel-divider {
  height: 1px;
  background-color: var(--border-secondary);
  margin: var(--space-lg) 0;
  border: none;
}

/* ==========================================================================
   PANEL LAYOUTS
========================================================================== */

.panel-grid {
  display: grid;
  gap: var(--space-lg);
}

.panel-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.panel-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.panel-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.panel-flex {
  display: flex;
  gap: var(--space-md);
}

.panel-flex-column {
  flex-direction: column;
}

.panel-flex-wrap {
  flex-wrap: wrap;
}

.panel-stack {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

/* ==========================================================================
   SPECIAL PANEL TYPES
========================================================================== */

/* Trading Panel */
.panel-trading {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border: 1px solid var(--border-accent);
  position: relative;
}

.panel-trading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

/* Chart Panel */
.panel-chart {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  padding: var(--space-md);
  min-height: var(--chart-height-md);
}

.panel-chart .panel-header {
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
}

/* Metric Panel */
.panel-metric {
  text-align: center;
  padding: var(--space-lg);
  background: var(--gradient-dark);
  border: 1px solid var(--border-primary);
  transition: all var(--duration-normal) var(--ease-out);
}

.panel-metric:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--border-accent);
}

/* Status Panel */
.panel-status {
  padding: var(--space-md);
  border-left: 4px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.panel-status.status-success {
  border-left-color: var(--positive);
  background-color: var(--positive-alpha);
}

.panel-status.status-warning {
  border-left-color: var(--warning);
  background-color: var(--warning-alpha);
}

.panel-status.status-error {
  border-left-color: var(--negative);
  background-color: var(--negative-alpha);
}

.panel-status.status-info {
  border-left-color: var(--info);
  background-color: var(--info-alpha);
}

/* ==========================================================================
   PANEL ANIMATIONS
========================================================================== */

.panel-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.panel-slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.panel-slide-down {
  animation: slideDown var(--duration-normal) var(--ease-out);
}

.panel-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-spring);
}

/* ==========================================================================
   RESPONSIVE PANELS
========================================================================== */

@media (max-width: 768px) {
  .panel-base {
    padding: var(--space-md);
    border-radius: var(--radius-md);
  }
  
  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .panel-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .panel-grid-2,
  .panel-grid-3,
  .panel-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .panel-flex {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .panel-base {
    padding: var(--space-sm);
    margin: var(--space-xs);
  }
  
  .panel-title {
    font-size: var(--text-base);
  }
  
  .panel-footer {
    flex-direction: column;
    align-items: stretch;
  }
}

/* ==========================================================================
   PANEL KEYFRAMES
========================================================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ==========================================================================
   PANEL UTILITIES
========================================================================== */

.panel-full-height {
  height: 100%;
}

.panel-min-height {
  min-height: 200px;
}

.panel-scrollable {
  overflow-y: auto;
  max-height: 400px;
}

.panel-no-padding {
  padding: 0;
}

.panel-no-border {
  border: none;
}

.panel-no-shadow {
  box-shadow: none;
}

.panel-centered {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.panel-sticky {
  position: sticky;
  top: var(--space-lg);
  z-index: var(--z-sticky);
}