"""
Pydantic models specifically related to the internal workings and directive
outputs of the Adaptive Trade Idea Framework (ATIF) in EOTS v2.5.
These schemas define data structures for situational assessment, strategic
directives before parameterization, and management actions for active trades.
"""
from pydantic import BaseModel, Field, ConfigDict # Added ConfigDict
from typing import Dict, Any, Optional
from datetime import datetime

# Forward reference if TickerContextDictV2_5 is in another file and imported via __init__
# from .context_schemas import TickerContextDictV2_5 # Example if needed

class ATIFSituationalAssessmentProfileV2_5(BaseModel):
    """
    Represents ATIF's initial, holistic assessment of the current market situation
    after integrating and weighing all incoming signals, market regime, and ticker context.
    This is an intermediate data structure within ATIF before final conviction.
    """
    bullish_assessment_score: Optional[float] = Field(default=None, description="ATIF's aggregated weighted score for a bullish outlook.") # Refactored
    bearish_assessment_score: Optional[float] = Field(default=None, description="ATIF's aggregated weighted score for a bearish outlook.") # Refactored
    vol_expansion_score: Optional[float] = Field(default=None, description="ATIF's assessment of the likelihood/strength of potential volatility expansion.") # Refactored
    vol_contraction_score: Optional[float] = Field(default=None, description="ATIF's assessment for potential volatility contraction.") # Refactored
    mean_reversion_likelihood: Optional[float] = Field(default=None, description="ATIF's assessment of conditions favoring mean reversion strategies.") # Refactored
    timestamp: datetime = Field(..., description="Timestamp of when this assessment profile was generated by ATIF.")
    # Additional specialized assessment scores can be added here as ATIF evolves.
    # Example: pinning_opportunity_score: Optional[float] = Field(default=None)

    model_config = ConfigDict(extra='forbid')


class ATIFStrategyDirectivePayloadV2_5(BaseModel):
    """
    Represents a strategic directive formulated by ATIF when it identifies a
    potential trading opportunity with sufficient conviction. This payload specifies
    the type of options strategy, target DTE/delta ranges, and ATIF's conviction,
    serving as input to the TradeParameterOptimizerV2_5.
    """
    selected_strategy_type: str = Field(..., description="The category of options strategy selected by ATIF (e.g., 'LongCall', 'BullPutSpread', 'ShortIronCondor').")
    target_dte_min: int = Field(..., ge=0, description="Minimum Days-To-Expiration for the desired option(s).")
    target_dte_max: int = Field(..., ge=0, description="Maximum Days-To-Expiration for the desired option(s).")
    target_delta_long_leg_min: Optional[float] = Field(None, description="Target minimum delta for the long leg(s) of the strategy (e.g., 0.3 for 30 delta).")
    target_delta_long_leg_max: Optional[float] = Field(None, description="Target maximum delta for the long leg(s) of the strategy (e.g., 0.7 for 70 delta).")
    target_delta_short_leg_min: Optional[float] = Field(None, description="Target minimum delta for the short leg(s) of a spread (absolute value, e.g., 0.1 for 10 delta).")
    target_delta_short_leg_max: Optional[float] = Field(None, description="Target maximum delta for the short leg(s) of a spread (absolute value, e.g., 0.4 for 40 delta).")
    underlying_price_at_decision: float = Field(..., description="The price of the underlying asset when ATIF made this strategic decision.")
    final_conviction_score_from_atif: float = Field(..., description="ATIF's final conviction score for this strategic idea (e.g., on a 0-5 scale).")
    supportive_rationale_components: Dict[str, Any] = Field(default_factory=dict, description="Key data points, signals, or reasons supporting this directive.")
    assessment_profile: ATIFSituationalAssessmentProfileV2_5 = Field(..., description="The detailed situational assessment that led to this directive.")

    model_config = ConfigDict(extra='forbid')


class ATIFManagementDirectiveV2_5(BaseModel):
    """
    Represents a specific management action directive issued by ATIF for an
    existing, active trade recommendation. It instructs the ITSOrchestratorV2_5
    on how to modify an active trade based on evolving market conditions.
    """
    recommendation_id: str = Field(..., description="The unique ID of the active recommendation this directive applies to.")
    action: str = Field(..., description="The specific management action to take (e.g., 'EXIT', 'ADJUST_STOPLOSS', 'ADJUST_TARGET_1', 'PARTIAL_PROFIT_TAKE', 'HOLD').")
    reason: str = Field(..., description="A human-readable reason for the directive (e.g., 'RegimeInvalidation_BearishShift', 'ATR_TrailingStop_Activated').")
    new_stop_loss: Optional[float] = Field(None, description="The new stop-loss price (option premium or underlying), if the action is to adjust it.")
    new_target_1: Optional[float] = Field(None, description="The new first profit target price, if applicable.")
    new_target_2: Optional[float] = Field(None, description="The new second profit target price, if applicable.")
    exit_price_type: Optional[str] = Field(None, description="Suggested execution type for an exit (e.g., 'Market', 'Limit').")
    percentage_to_manage: Optional[float] = Field(None, ge=0, le=1, description="For partial actions (e.g., PARTIAL_PROFIT_TAKE), the percentage of the position to affect (0.0 to 1.0).")

    model_config = ConfigDict(extra='forbid')

__all__ = [
    "ATIFSituationalAssessmentProfileV2_5",
    "ATIFStrategyDirectivePayloadV2_5",
    "ATIFManagementDirectiveV2_5",
]