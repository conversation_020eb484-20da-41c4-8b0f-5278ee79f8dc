/* AI Dashboard Styling for EOTS v2.5 */

.ai-dashboard-container {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    padding: 20px;
    color: #ffffff;
    overflow-x: hidden; /* CRITICAL FIX: Prevent horizontal scrolling */
}

/* CRITICAL FIX: Improve main container layout */
.container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    margin: 0 auto;
}

/* CRITICAL FIX: Ensure proper row spacing and prevent overlapping */
.row {
    margin-left: -10px;
    margin-right: -10px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch; /* Make all columns same height */
}

.ai-analysis-card {
    background: linear-gradient(145deg, #1e1e2e, #2a2a3e);
    border: 1px solid #00d4ff;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.ai-analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 212, 255, 0.2);
}

.ai-recommendations-card {
    background: linear-gradient(145deg, #2e1e1e, #3e2a2a);
    border: 1px solid #ffd93d;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(255, 217, 61, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.ai-recommendations-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(255, 217, 61, 0.2);
}

.ai-regime-card {
    background: linear-gradient(145deg, #1e2e1e, #2a3e2a);
    border: 1px solid #6bcf7f;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(107, 207, 127, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.ai-regime-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(107, 207, 127, 0.2);
}

.ai-insights-card {
    background: linear-gradient(145deg, #2e2e1e, #3e3e2a);
    border: 1px solid #ffd93d;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(255, 217, 61, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.ai-insights-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(255, 217, 61, 0.2);
}

.ai-performance-card {
    background: linear-gradient(145deg, #1e2e2e, #2a3e3e);
    border: 1px solid #6bcf7f;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(107, 207, 127, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.ai-performance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(107, 207, 127, 0.2);
}

.ai-insight-text {
    background: rgba(0, 212, 255, 0.1);
    border-left: 4px solid #00d4ff;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.95em;
    line-height: 1.5;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.ai-insight-text:hover {
    background: rgba(0, 212, 255, 0.15);
    transform: translateX(5px);
}

.ai-insights-container {
    min-height: 300px;
    max-height: 80vh;
    overflow-y: auto;
    padding-right: 10px;
    resize: vertical;
}

.ai-insights-container::-webkit-scrollbar {
    width: 6px;
}

.ai-insights-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.ai-insights-container::-webkit-scrollbar-thumb {
    background: #00d4ff;
    border-radius: 3px;
}

.recommendation-item {
    background: rgba(255, 217, 61, 0.1);
    border: 1px solid rgba(255, 217, 61, 0.3);
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
}

.recommendation-item:hover {
    background: rgba(255, 217, 61, 0.15);
    border-color: rgba(255, 217, 61, 0.5);
    transform: scale(1.02);
}

.recommendations-container {
    min-height: 350px;
    max-height: 80vh;
    overflow-y: auto;
    padding-right: 10px;
    resize: vertical;
}

.recommendations-container::-webkit-scrollbar {
    width: 6px;
}

.recommendations-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.recommendations-container::-webkit-scrollbar-thumb {
    background: #ffd93d;
    border-radius: 3px;
}

.regime-analysis-container {
    background: rgba(107, 207, 127, 0.1);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(107, 207, 127, 0.2);
}

.insight-item {
    background: rgba(255, 217, 61, 0.1);
    border-radius: 8px;
    padding: 10px 15px;
    border-left: 3px solid #ffd93d;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.insight-item:hover {
    background: rgba(255, 217, 61, 0.15);
    transform: translateX(5px);
}

.insights-container {
    min-height: 250px;
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
    resize: vertical;
}

.insights-container::-webkit-scrollbar {
    width: 6px;
}

.insights-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.insights-container::-webkit-scrollbar-thumb {
    background: #ffd93d;
    border-radius: 3px;
}

/* AI Dashboard Header */
.dashboard-title {
    background: linear-gradient(90deg, #00d4ff, #ffd93d, #6bcf7f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.dashboard-subtitle {
    text-align: center;
    color: #cccccc;
    font-size: 1.1em;
    margin-bottom: 30px;
    line-height: 1.6;
}

/* Card Titles */
.card-title {
    background: linear-gradient(90deg, #00d4ff, #ffd93d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    padding-bottom: 10px;
}

/* Animations */
@keyframes pulse-glow {
    0% { box-shadow: 0 0 5px rgba(0, 212, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.6); }
    100% { box-shadow: 0 0 5px rgba(0, 212, 255, 0.3); }
}

.ai-analysis-card {
    animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: slide-in 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-dashboard-container {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
        padding: 15px;
    }
    
    .dashboard-title {
        font-size: 1.5em;
    }
    
    .dashboard-subtitle {
        font-size: 1em;
    }
}

/* System Status Bar - Fixed positioning to prevent scrolling interference */
.system-status-bar {
    position: relative !important;
    z-index: 1 !important;
    overflow: hidden !important;
    max-width: 100% !important;
    contain: layout style !important;
}

.dashboard-header {
    position: relative !important;
    z-index: 2 !important;
    contain: layout style !important;
}

/* Badge Enhancements */
.badge {
    font-size: 0.8em;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Alert Enhancements */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));
    color: #00d4ff;
    border-left: 4px solid #00d4ff;
}

/* Performance Metrics Grid */
.row .col-md-3 {
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px; /* FIXED: Increase spacing between cards */
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    min-height: 200px; /* FIXED: Ensure consistent height */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.row .col-md-3:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* CRITICAL FIX: Improve row layout to prevent overlapping */
.command-center-row,
.core-metrics-row,
.system-health-row {
    margin-bottom: 25px !important;
    padding: 10px 0;
    overflow: hidden; /* Prevent content overflow */
}

/* CRITICAL FIX: Ensure proper column spacing */
.col-md-4, .col-md-6, .col-md-3 {
    padding-left: 10px !important;
    padding-right: 10px !important;
    margin-bottom: 20px;
}

/* CRITICAL FIX: Prevent card content overflow */
.elite-card {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 250px; /* Ensure minimum height for consistency */
}

.elite-card .card-body {
    flex: 1;
    overflow-y: auto;
    min-height: 350px; /* Ensure minimum height for charts */
    max-height: 80vh; /* Responsive maximum height */
    padding: 15px;
    resize: vertical; /* Allow user to resize vertically */
}

/* Responsive Chart Container Fixes */
.chart-container, .plotly-chart-container {
    width: 100% !important;
    height: auto !important;
    min-height: 400px;
    max-height: 80vh;
    overflow: visible;
    resize: both;
}

/* Fix for Plotly charts being cut off */
.js-plotly-plot, .plotly {
    width: 100% !important;
    height: 100% !important;
}

/* Ensure chart containers auto-adjust */
.chart-wrapper {
    width: 100%;
    height: auto;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

/* Fix for dashboard grid layout */
.dashboard-grid-item {
    min-height: 400px;
    height: auto !important;
    overflow: visible;
}

/* Error Container */
.error-container {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
    border: 1px solid #ff6b6b;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    margin: 50px auto;
    max-width: 600px;
}

/* Fix for missing Bootstrap text-muted class */
.text-muted {
    color: #6c757d !important;
    opacity: 0.75;
}

/* Additional Bootstrap fixes if needed */
.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }
