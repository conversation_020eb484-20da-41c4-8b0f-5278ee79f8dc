#!/usr/bin/env python
# elite_options_system_v2_5/__init__.py

"""
Elite Options Trading System v2.5 ("Apex Predator")

This package contains the core components of the Elite Options Trading System,
including data management, analytics engines, dashboard applications, and
AI-powered trading intelligence.

This __init__.py file marks the directory as a Python package and provides
basic package metadata.
"""

__version__ = "2.5.0"
__author__ = "Elite Options Trading System Team"

# Package initialization - no additional setup required
# This file ensures the directory is treated as a proper Python package
