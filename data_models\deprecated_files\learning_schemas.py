from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict # Added ConfigDict
from .learning_config_schemas import (
    MarketContextData, AdaptationSuggestion, PerformanceMetricsSnapshot,
    LearningInsightData, ExpertAdaptationSummary, ConfidenceUpdateData,
    OptimizationRecommendation, LearningMetadata, IntelligenceLayerData,
    MetaLearningData, PatternFeatures, MarketPrediction, PatternMetaAnalysis
)

class LearningInsightV2_5(BaseModel):
    """Represents a single learning insight generated by the HuiHui system."""
    insight_id: str = Field(..., description="Unique identifier for the insight.")
    insight_type: str = Field(..., description="Category of the insight (e.g., 'performance_anomaly', 'pattern_discovery').")
    insight_description: str = Field(..., description="Detailed description of the insight.")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence level of the insight (0.0 to 1.0).")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Timestamp when the insight was generated.")
    market_context: MarketContextData = Field(default_factory=MarketContextData, description="Market conditions or context relevant to the insight.")
    suggested_adaptation: AdaptationSuggestion = Field(default_factory=AdaptationSuggestion, description="Suggested adaptation parameters based on the insight.")
    performance_metrics_pre: PerformanceMetricsSnapshot = Field(default_factory=PerformanceMetricsSnapshot, description="Performance metrics before the insight was applied.")
    performance_metrics_post: PerformanceMetricsSnapshot = Field(default_factory=PerformanceMetricsSnapshot, description="Expected performance metrics after the insight is applied.")
    integration_priority: int = Field(5, ge=1, le=10, description="Priority for integrating this insight (1=highest, 10=lowest).")
    integration_complexity: int = Field(5, ge=1, le=10, description="Complexity of applying the suggested adaptation (1=low, 10=high).")
    adaptation_type: Optional[str] = Field(None, description="Specific type of adaptation suggested (e.g., 'parameter_adjustment', 'model_retraining').")
    errors: List[str] = Field(default_factory=list, description="List of errors encountered during insight generation or processing.")
    model_config = ConfigDict(extra='forbid')


class UnifiedLearningResult(BaseModel):
    """Comprehensive result of a unified learning cycle, summarizing insights and adaptations."""
    symbol: str = Field(..., description="Symbol for which learning was performed (or 'SYSTEM' for system-wide).")
    analysis_timestamp: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of this learning analysis.")
    learning_insights: LearningInsightData = Field(default_factory=LearningInsightData, description="Summarized learning insights.")
    performance_improvements: PerformanceMetricsSnapshot = Field(default_factory=PerformanceMetricsSnapshot, description="Quantified performance improvements from adaptations.")
    expert_adaptations: ExpertAdaptationSummary = Field(default_factory=ExpertAdaptationSummary, description="Summary of adaptations made by expert systems.")
    confidence_updates: ConfidenceUpdateData = Field(default_factory=ConfidenceUpdateData, description="Updates on confidence scores over time.")
    next_learning_cycle: datetime = Field(..., description="Timestamp for the next scheduled learning cycle.")
    learning_cycle_type: str = Field(..., description="Type of learning cycle (e.g., 'daily', 'weekly', 'continuous').")
    lookback_period_days: int = Field(..., ge=1, description="Number of days considered for this learning cycle.")
    performance_improvement_score: float = Field(..., ge=0.0, le=1.0, description="Overall score indicating performance improvement (0.0 to 1.0).")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Overall confidence score of the learning system.")
    optimization_recommendations: List[OptimizationRecommendation] = Field(default_factory=list, description="Specific recommendations for further optimization.")
    eots_schema_compliance: bool = Field(True, description="Indicates if the learning result is compliant with EOTS schemas.")
    learning_metadata: LearningMetadata = Field(default_factory=LearningMetadata, description="Additional metadata about the learning process.")
    model_config = ConfigDict(extra='forbid')

class MarketPattern(BaseModel):
    """Represents a detected market pattern."""
    pattern_id: str = Field(..., description="Unique pattern identifier")
    symbol: str = Field(..., description="Trading symbol")
    pattern_type: str = Field(..., description="Type of pattern detected")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Pattern confidence")
    success_rate: float = Field(..., ge=0.0, le=1.0, description="Historical success rate")
    market_conditions: MarketContextData = Field(description="Market conditions when pattern occurred")
    eots_metrics: Dict[str, float] = Field(description="EOTS metrics at pattern time")
    outcome: Optional[str] = Field(None, description="Pattern outcome if known")
    learning_weight: float = Field(default=1.0, description="Weight for learning algorithm")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of pattern detection.")
    model_config = ConfigDict(extra='forbid')

class PatternThresholds(BaseModel):
    """Defines thresholds for various market pattern detections."""
    volatility_expansion: float = Field(..., description="Threshold for volatility expansion pattern.")
    trend_continuation: float = Field(..., description="Threshold for trend continuation pattern.")
    accumulation: float = Field(..., description="Threshold for accumulation pattern.")
    distribution: float = Field(..., description="Threshold for distribution pattern.")
    consolidation: float = Field(..., description="Threshold for consolidation pattern.")
    significant_pos_thresh: float = Field(..., description="Significant positive threshold.")
    dwfd_strong_thresh: float = Field(..., description="DWFD strong threshold.")
    moderate_confidence_thresh: float = Field(..., description="Moderate confidence threshold.")
    model_config = ConfigDict(extra='forbid')

class AdaptiveLearningResult(BaseModel):
    """Pydantic model for adaptive learning results."""
    learning_iteration: int = Field(description="Current learning iteration")
    patterns_analyzed: int = Field(description="Number of patterns analyzed")
    accuracy_improvement: float = Field(description="Accuracy improvement percentage")
    new_insights: List[str] = Field(description="New insights discovered")
    confidence_evolution: float = Field(description="Evolution in confidence scoring")
    adaptation_score: float = Field(ge=0.0, le=10.0, description="Overall adaptation score")
    model_config = ConfigDict(extra='forbid')

class RecursiveIntelligenceResult(BaseModel):
    """Pydantic model for recursive intelligence analysis."""
    analysis_depth: int = Field(description="Depth of recursive analysis")
    intelligence_layers: List[IntelligenceLayerData] = Field(description="Layers of intelligence")
    convergence_score: float = Field(description="Analysis convergence score")
    recursive_insights: List[str] = Field(description="Insights from recursive analysis")
    meta_learning_data: MetaLearningData = Field(description="Meta-learning information")
    model_config = ConfigDict(extra='forbid')

class MarketIntelligencePattern(BaseModel):
    """Represents a market intelligence pattern detected by the system."""
    pattern_id: str = Field(..., description="Unique identifier for the pattern")
    pattern_type: str = Field(..., description="Type of intelligence pattern")
    symbol: str = Field(..., description="Trading symbol")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Pattern detection timestamp")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Pattern confidence score")
    market_conditions: MarketContextData = Field(..., description="Market conditions when pattern was detected")
    intelligence_metrics: Dict[str, float] = Field(..., description="Intelligence metrics at pattern time")
    pattern_features: PatternFeatures = Field(..., description="Key features of the pattern")
    historical_accuracy: float = Field(..., ge=0.0, le=1.0, description="Historical accuracy of similar patterns")
    prediction: Optional[MarketPrediction] = Field(None, description="Pattern-based market prediction")
    validation_metrics: Dict[str, float] = Field(default_factory=dict, description="Pattern validation metrics")
    meta_analysis: PatternMetaAnalysis = Field(default_factory=PatternMetaAnalysis, description="Meta-analysis of the pattern")
    model_config = ConfigDict(extra='forbid')

# --- Models moved from dashboard_application/modes/ai_dashboard/eots_learning_integration_v2_5.py ---

class EOTSLearningContext(BaseModel):
    """Pydantic model for EOTS learning context."""
    symbol: str = Field(..., description="Trading symbol")
    current_regime: str = Field(..., description="Current market regime")
    signal_strength: float = Field(..., ge=0.0, le=5.0, description="Overall signal strength")
    confidence_level: float = Field(..., ge=0.0, le=1.0, description="System confidence")
    market_conditions: Dict[str, Any] = Field(default_factory=dict, description="Market conditions")
    eots_metrics: Dict[str, Any] = Field(default_factory=dict, description="EOTS metrics snapshot")
    model_config = ConfigDict(extra='forbid')

class EOTSPredictionOutcome(BaseModel):
    """Pydantic model for EOTS prediction outcome validation."""
    prediction_id: str = Field(..., description="Original prediction ID")
    actual_regime: str = Field(..., description="Actual market regime that occurred")
    regime_accuracy: float = Field(..., ge=0.0, le=1.0, description="Regime prediction accuracy")
    signal_performance: float = Field(..., ge=0.0, le=1.0, description="Signal performance score")
    market_events: List[str] = Field(default_factory=list, description="Actual market events")
    outcome_timestamp: datetime = Field(default_factory=datetime.now, description="When outcome was recorded")
    learning_feedback: Dict[str, Any] = Field(default_factory=dict, description="Learning feedback data")
    model_config = ConfigDict(extra='forbid')

# --- END MOVED MODELS ---

__all__ = [
    "LearningInsightV2_5",
    "UnifiedLearningResult",
    "MarketPattern",
    "PatternThresholds",
    "AdaptiveLearningResult",
    "RecursiveIntelligenceResult",
    "MarketIntelligencePattern",
    "EOTSLearningContext", # Added
    "EOTSPredictionOutcome", # Added
]