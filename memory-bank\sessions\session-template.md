# Session Memory Template

**Session Date**: [YYYY-MM-DD]
**Session ID**: [Unique identifier]
**Duration**: [Start time - End time]
**Context**: [Brief description of session focus]

## Session Objectives
- [ ] Objective 1
- [ ] Objective 2
- [ ] Objective 3

## Knowledge State at Start
### Previous Context
- Last session focus: [Description]
- Pending issues: [List]
- Active patterns: [Patterns being applied]

### Memory Bank Status
- Core knowledge areas: [List]
- Recent additions: [List]
- Knowledge gaps identified: [List]

## Session Activities

### Problems Encountered
1. **Problem**: [Description]
   - **Context**: [Situation]
   - **Approach**: [How addressed]
   - **Outcome**: [Result]
   - **Learning**: [What was learned]

### Solutions Developed
1. **Solution**: [Description]
   - **Problem Addressed**: [Reference]
   - **Method**: [Approach used]
   - **Implementation**: [How executed]
   - **Validation**: [How verified]

### Patterns Applied
1. **Pattern**: [Name/Description]
   - **Context**: [Where applied]
   - **Effectiveness**: [How well it worked]
   - **Adaptations**: [Modifications made]
   - **Lessons**: [What was learned]

### New Discoveries
1. **Discovery**: [Description]
   - **Context**: [How discovered]
   - **Significance**: [Why important]
   - **Connections**: [Links to existing knowledge]
   - **Implications**: [What this enables]

## Knowledge Evolution

### New Entities Created
- [Entity name]: [Description and significance]

### Relationships Established
- [Entity A] -> [Relationship] -> [Entity B]: [Significance]

### Patterns Identified
- [Pattern name]: [Description and context]

### Knowledge Refined
- [Knowledge area]: [How understanding improved]

## Session Outcomes

### Objectives Achieved
- [x] Completed objective 1
- [ ] Partially completed objective 2
- [ ] Deferred objective 3

### Unexpected Results
- [Description of unexpected outcomes]

### Quality Metrics
- **Problems Solved**: [Count]
- **New Knowledge Nodes**: [Count]
- **Patterns Applied**: [Count]
- **Connections Made**: [Count]

## Next Session Preparation

### Carry Forward
- [ ] Unfinished task 1
- [ ] Follow-up investigation 2
- [ ] Pattern validation 3

### Knowledge Gaps to Address
- [Gap 1]: [Why important]
- [Gap 2]: [Priority level]

### Patterns to Explore
- [Pattern 1]: [Context for exploration]
- [Pattern 2]: [Expected application]

## Memory Integration

### Core Memory Updates
- [File]: [Changes made]

### Knowledge Graph Updates
- [Entities added/modified]
- [Relationships created/updated]

### Evolution Memory
- [How thinking evolved this session]
- [New cognitive patterns emerged]

## Session Reflection

### What Worked Well
- [Successful approaches]

### What Could Improve
- [Areas for enhancement]

### Cognitive Evolution
- [How thinking patterns evolved]
- [New mental models developed]

### Confidence Changes
- [Areas where confidence increased]
- [Areas where uncertainty emerged]

---

**Session Status**: [Active/Completed/Archived]
**Next Session Focus**: [Planned direction]
**Memory Bank Impact**: [Significance of session learnings]