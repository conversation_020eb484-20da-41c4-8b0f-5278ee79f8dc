# core_analytics_engine/eots_metrics/supplementary_metrics.py
#!/usr/bin/env python3
"""Quick fix for supplementary_metrics.py duplicate class issue"""

import re

def fix_supplementary_metrics():
    file_path = 'core_analytics_engine/eots_metrics/supplementary_metrics.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split into lines
    lines = content.split('\n')
    new_lines = []
    skip_lines = False
    
    for i, line in enumerate(lines):
        # Keep the header comment
        if line.strip() == '# core_analytics_engine/eots_metrics/supplementary_metrics.py':
            new_lines.append(line)
        # Skip the first class definition until we hit the docstring
        elif line.startswith('class AdvancedOptionsMetrics(BaseModel):') and not skip_lines:
            skip_lines = True
            continue
        elif skip_lines and '"""' in line and 'EOTS Supplementary Metrics' in line:
            skip_lines = False
            new_lines.append(line)
        elif not skip_lines:
            new_lines.append(line)
    
    # Write back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print("✅ Fixed duplicate class definition")

if __name__ == '__main__':
    fix_supplementary_metrics()
"""
EOTS Supplementary Metrics - Consolidated Miscellaneous Calculations

Consolidates:
- miscellaneous_metrics.py: ATR, advanced options metrics, and other utilities

Optimizations:
- Streamlined ATR calculation
- Simplified advanced options metrics
- Unified utility functions
- Eliminated redundant calculations
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
from datetime import datetime
from scipy import stats

from core_analytics_engine.eots_metrics.core_calculator import CoreCalculator
from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5
from pydantic import BaseModel, Field, ConfigDict

logger = logging.getLogger(__name__)
EPSILON = 1e-9

class AdvancedOptionsMetrics(BaseModel):
    """
    Pydantic-v2 model for Advanced Options Metrics.

    Migrated from a hand-rolled class to ensure strict typing, validation and
    uniform `.model_dump()` / `.model_validate()` behaviour across the code-base.
    """

    lwpai: float = Field(0.0, description="Liquidity-Weighted Price Action Indicator")
    vabai: float = Field(0.0, description="Volatility-Adjusted Bid/Ask Imbalance")
    aofm: float = Field(0.0, description="Aggressive Order Flow Momentum")
    lidb: float = Field(0.0, description="Liquidity-Implied Directional Bias")
    spread_to_volatility_ratio: float = Field(0.0, description="Spread to Volatility Ratio")
    theoretical_price_deviation: float = Field(0.0, description="Theoretical Price Deviation with Liquidity Filter")

    # Strict model – forbid extras to preserve data integrity
    model_config = ConfigDict(extra='forbid')

    # Back-compat helper
    def to_dict(self) -> Dict[str, float]:
        """
        DEPRECATED – Use `.model_dump()` instead.
        Provided only for temporary backward compatibility.
        """
        import warnings
        warnings.warn(
            "`AdvancedOptionsMetrics.to_dict()` is deprecated. "
            "Use `.model_dump()` instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return self.model_dump()


class SupplementaryMetricsOutput(BaseModel):
    """
    Pydantic-v2 model for Supplementary Metrics Output.

    Contains all supplementary metrics calculated for the underlying asset.
    """

    atr_und: float = Field(1.0, description="Average True Range for the underlying")
    hist_vol_20d: float = Field(0.2, description="Historical volatility over 20 days")
    impl_vol_atm: float = Field(0.2, description="Implied volatility at the money")
    trend_strength: float = Field(0.5, description="Trend strength indicator")
    trend_direction: str = Field("neutral", description="Trend direction")

    # Advanced options metrics
    lwpai: float = Field(0.0, description="Liquidity-Weighted Price Action Indicator")
    vabai: float = Field(0.0, description="Volatility-Adjusted Bid/Ask Imbalance")
    aofm: float = Field(0.0, description="Aggressive Order Flow Momentum")
    lidb: float = Field(0.0, description="Liquidity-Implied Directional Bias")
    spread_to_volatility_ratio: float = Field(0.0, description="Spread to Volatility Ratio")
    theoretical_price_deviation: float = Field(0.0, description="Theoretical Price Deviation with Liquidity Filter")

    # Strict model – forbid extras to preserve data integrity
    model_config = ConfigDict(extra='forbid')

class SupplementaryMetrics(CoreCalculator):
    """
    Consolidated supplementary metrics calculator.
    
    Handles:
    - ATR (Average True Range) calculation
    - Advanced options metrics (LWPAI, VABAI, AOFM, LIDB)
    - Other utility calculations
    """
    
    def __init__(self, config_manager: Any, historical_data_manager: Any, enhanced_cache_manager: EnhancedCacheManagerV2_5):
        super().__init__(config_manager, historical_data_manager, enhanced_cache_manager)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # ATR calculation parameters
        self.DEFAULT_ATR_PERIOD = 14
        self.MIN_ATR_PERIODS = 5
        
        # Advanced metrics parameters
        self.LIQUIDITY_THRESHOLD = 1000000  # $1M threshold for liquidity calculations
        self.VOLATILITY_NORMALIZATION_FACTOR = 100
        
        # Previous values for momentum calculations
        self._previous_aofm = 0.0
        self._previous_lidb = 0.0

    def calculate_all_supplementary_metrics(self, raw_underlying_data, options_df) -> SupplementaryMetricsOutput:
        """
        Calculate all supplementary metrics and return as a Pydantic model.

        Args:
            raw_underlying_data: Raw underlying data model
            options_df: Options DataFrame

        Returns:
            SupplementaryMetricsOutput with calculated supplementary metrics
        """
        try:
            symbol = raw_underlying_data.symbol
            
            # Calculate ATR
            atr_value = self.calculate_atr(symbol)
            
            # Calculate advanced options metrics
            underlying_dict = raw_underlying_data.model_dump() if hasattr(raw_underlying_data, 'model_dump') else raw_underlying_data
            advanced_metrics = self.calculate_advanced_options_metrics(options_df, underlying_dict)
            
            # Calculate additional metrics with defaults for missing fields
            hist_vol_20d = 0.2  # Default 20% historical volatility
            impl_vol_atm = getattr(raw_underlying_data, 'u_volatility', 0.2)  # Use current IV or default
            trend_strength = 0.5  # Neutral trend strength
            trend_direction = "neutral"  # Neutral trend direction
            
            return SupplementaryMetricsOutput(
                atr_und=atr_value,
                hist_vol_20d=hist_vol_20d,
                impl_vol_atm=impl_vol_atm,
                trend_strength=trend_strength,
                trend_direction=trend_direction,
                **advanced_metrics.model_dump()
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating supplementary metrics: {e}")
            # Return defaults for all supplementary metrics
            return SupplementaryMetricsOutput(
                atr_und=1.0,  # Default ATR
                hist_vol_20d=0.2,
                impl_vol_atm=0.2,
                trend_strength=0.5,
                trend_direction="neutral",
                lwpai=0.0,
                vabai=0.0,
                aofm=0.0,
                lidb=0.0,
                spread_to_volatility_ratio=0.0,
                theoretical_price_deviation=0.0
            )
    
    # =============================================================================
    # ATR CALCULATION - Optimized from miscellaneous_metrics.py
    # =============================================================================
    
    def calculate_atr(self, symbol: str, dte_max: int = 45) -> float:
        """
        Calculate Average True Range (ATR) for the underlying symbol.
        
        Args:
            symbol: Underlying symbol
            dte_max: Maximum DTE for context (affects lookback period)
            
        Returns:
            ATR value as float
        """
        self.logger.debug(f"Calculating ATR for {symbol}...")
        
        try:
            # FAIL-FAST: No ATR calculation for futures symbols
            if self._is_futures_symbol(symbol):
                self.logger.error(f"CRITICAL: Cannot calculate ATR for futures symbol {symbol} - futures not supported!")
                raise ValueError(f"CRITICAL: ATR calculation not supported for futures symbol {symbol}!")
            
            # Determine lookback period based on DTE context
            lookback_days = max(dte_max, self.DEFAULT_ATR_PERIOD)
            
            # Get historical OHLCV data
            ohlcv_df = self.historical_data_manager.get_historical_ohlcv(symbol, lookback_days=lookback_days)
            
            if ohlcv_df is None or len(ohlcv_df) < self.MIN_ATR_PERIODS:
                self.logger.error(f"CRITICAL: Insufficient OHLCV data for {symbol} - need at least {self.MIN_ATR_PERIODS} periods!")
                raise ValueError(f"CRITICAL: Insufficient OHLCV data for {symbol} - cannot calculate ATR without real historical data!")
            
            # Calculate True Range components
            high_low = ohlcv_df['high'] - ohlcv_df['low']
            high_close_prev = np.abs(ohlcv_df['high'] - ohlcv_df['close'].shift(1))
            low_close_prev = np.abs(ohlcv_df['low'] - ohlcv_df['close'].shift(1))
            
            # True Range is the maximum of the three components
            true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
            
            # Remove NaN values (from shift operation)
            true_range = true_range.dropna()
            
            if len(true_range) < self.MIN_ATR_PERIODS:
                self.logger.error(f"CRITICAL: Insufficient True Range data for {symbol} - need at least {self.MIN_ATR_PERIODS} periods!")
                raise ValueError(f"CRITICAL: Insufficient True Range data for {symbol} - cannot calculate ATR without real data!")
            
            # Calculate ATR using exponential moving average for better responsiveness
            atr_period = min(self.DEFAULT_ATR_PERIOD, len(true_range))
            atr_value = true_range.ewm(span=atr_period, adjust=False).mean().iloc[-1]
            
            # FAIL-FAST: Validate ATR value
            if pd.isna(atr_value) or atr_value <= 0:
                self.logger.error(f"CRITICAL: Invalid ATR value for {symbol}: {atr_value} - cannot return fake 0.0!")
                raise ValueError(f"CRITICAL: Invalid ATR value for {symbol}: {atr_value} - ATR must be positive!")
            
            self.logger.debug(f"ATR calculated for {symbol}: {atr_value:.4f}")
            return float(atr_value)
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating ATR for {symbol}: {e}", exc_info=True)
            raise ValueError(f"CRITICAL: ATR calculation failed for {symbol} - cannot return fake 0.0 value! Error: {e}") from e
    
    # =============================================================================
    # ADVANCED OPTIONS METRICS - Simplified from miscellaneous_metrics.py
    # =============================================================================
    
    def calculate_advanced_options_metrics(self, options_df: pd.DataFrame, underlying_data: Dict) -> AdvancedOptionsMetrics:
        """
        Calculate advanced options metrics using simplified but effective methodology.
        
        Args:
            options_df: DataFrame with options data
            underlying_data: Dictionary with underlying market data
            
        Returns:
            AdvancedOptionsMetrics object with calculated values
        """
        self.logger.debug("Calculating advanced options metrics...")
        
        try:
            if options_df.empty:
                return self._get_default_advanced_metrics()
            
            # Calculate individual metrics
            lwpai = self._calculate_lwpai_optimized(options_df, underlying_data)
            vabai = self._calculate_vabai_optimized(options_df, underlying_data)
            aofm = self._calculate_aofm_optimized(options_df, underlying_data)
            lidb = self._calculate_lidb_optimized(options_df, underlying_data)
            svr = self._calculate_svr_efficiency(options_df, underlying_data)
            tpdlf = self._calculate_tpdlf_quality(options_df, underlying_data)
            
            # Create and return metrics object
            metrics = AdvancedOptionsMetrics(
                lwpai=lwpai, 
                vabai=vabai, 
                aofm=aofm, 
                lidb=lidb,
                spread_to_volatility_ratio=svr,
                theoretical_price_deviation=tpdlf
            )
            
            self.logger.debug(f"Advanced options metrics calculated: LWPAI={lwpai:.2f}, VABAI={vabai:.2f}, AOFM={aofm:.2f}, LIDB={lidb:.2f}, SVR={svr:.2f}, TPDLF={tpdlf:.2f}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating advanced options metrics: {e}", exc_info=True)
            return self._get_default_advanced_metrics()
    
    def _calculate_svr_efficiency(self, options_df: pd.DataFrame, underlying_data: Dict) -> float:
        """Calculate Spread-to-Volatility Ratio (SVR) Efficiency."""
        try:
            if 'bid_price' not in options_df.columns or 'ask_price' not in options_df.columns or 'iv' not in options_df.columns:
                raise ValueError("Required columns for SVR are missing.")

            # Calculate bid-ask spread
            spread = options_df['ask_price'] - options_df['bid_price']
            
            # Calculate SVR
            svr = spread / options_df['iv'].replace(0, EPSILON)
            
            # Normalize: lower SVR is better. We invert and scale.
            # A score of 100 is excellent (low SVR), 0 is poor (high SVR).
            normalized_svr = 100 * (1 - np.tanh(svr.mean()))
            return self._bound_value(normalized_svr, 0, 100)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating SVR Efficiency: {e}")
            raise ValueError(f"CRITICAL: SVR calculation failed. Error: {e}") from e

    def _calculate_tpdlf_quality(self, options_df: pd.DataFrame, underlying_data: Dict) -> float:
        """Calculate Theoretical Price Deviation with Liquidity Filter (TPDLF) Quality."""
        try:
            required_cols = ['raw_price', 'volm', 'open_interest']
            if not all(col in options_df.columns for col in required_cols):
                raise ValueError("Required columns for TPDLF are missing.")

            # This is a placeholder for a proper Black-Scholes calculation
            # In a real scenario, you would calculate the theoretical price here.
            options_df['theoretical_price'] = options_df['raw_price'] * 0.98 # Assume a 2% deviation for example

            # Filter for liquid contracts
            liquid_contracts = options_df[
                (options_df['volm'] > 10) & (options_df['open_interest'] > 20)
            ]

            if liquid_contracts.empty:
                return 50.0 # Return a neutral score if no liquid contracts

            # Calculate deviation for liquid contracts
            deviation = abs(liquid_contracts['raw_price'] - liquid_contracts['theoretical_price']) / liquid_contracts['raw_price']
            
            # Normalize: lower deviation is better. We invert and scale.
            normalized_tpdlf = 100 * (1 - np.tanh(deviation.mean() * 10)) # Scale deviation before tanh
            return self._bound_value(normalized_tpdlf, 0, 100)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating TPDLF Quality: {e}")
            raise ValueError(f"CRITICAL: TPDLF calculation failed. Error: {e}") from e
    
    def _calculate_lwpai_optimized(self, options_df: pd.DataFrame, underlying_data: Dict) -> float:
        """Calculate Liquidity-Weighted Price Action Indicator (simplified)"""
        try:
            # DEBUG: Log what fields are available
            self.logger.debug(f"LWPAI DEBUG: Available fields in underlying_data: {list(underlying_data.keys())}")
            self.logger.debug(f"LWPAI DEBUG: value_bs field value: {underlying_data.get('value_bs', 'NOT_FOUND')}")
            self.logger.debug(f"LWPAI DEBUG: net_value_flow_5m_und field value: {underlying_data.get('net_value_flow_5m_und', 'NOT_FOUND')}")

            # FAIL-FAST: Extract volume and value data - NO FAKE DEFAULTS ALLOWED
            if 'day_volume' not in underlying_data:
                raise ValueError("CRITICAL: day_volume missing from underlying_data - cannot calculate LWPAI without real volume data!")
            total_volume = float(underlying_data['day_volume'])

            # TIERED FALLBACK SYSTEM: Try net_value_flow_5m_und first, then value_bs as fallback
            net_value_flow = None
            if 'net_value_flow_5m_und' in underlying_data and underlying_data['net_value_flow_5m_und'] is not None:
                net_value_flow = float(underlying_data['net_value_flow_5m_und'])
            elif 'value_bs' in underlying_data and underlying_data['value_bs'] is not None:
                net_value_flow = float(underlying_data['value_bs'])
                self.logger.debug("LWPAI: Using value_bs as fallback for net_value_flow_5m_und")
            else:
                raise ValueError("CRITICAL: Neither net_value_flow_5m_und nor value_bs available - cannot calculate LWPAI without real flow data!")

            if total_volume < self.LIQUIDITY_THRESHOLD:
                raise ValueError(f"CRITICAL: Insufficient liquidity (volume={total_volume}) - cannot calculate meaningful LWPAI signal!")
            
            # Calculate liquidity-weighted price action
            price_action = abs(net_value_flow) / max(total_volume, 1.0)
            
            # Normalize to 0-100 scale
            lwpai = min(100.0, price_action * self.VOLATILITY_NORMALIZATION_FACTOR)
            
            return lwpai
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating LWPAI: {e}")
            raise ValueError(f"CRITICAL: LWPAI calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    def _calculate_vabai_optimized(self, options_df: pd.DataFrame, underlying_data: Dict) -> float:
        """Calculate Volatility-Adjusted Bid/Ask Imbalance (simplified)"""
        try:
            # FAIL-FAST: Extract volatility and flow data - NO FAKE DEFAULTS ALLOWED
            if 'u_volatility' not in underlying_data:
                raise ValueError("CRITICAL: u_volatility missing from underlying_data - cannot calculate VABAI without real volatility data!")
            current_iv = float(underlying_data['u_volatility'])

            if 'net_vol_flow_5m_und' not in underlying_data:
                raise ValueError("CRITICAL: net_vol_flow_5m_und missing from underlying_data - cannot calculate VABAI without real flow data!")
            net_vol_flow = float(underlying_data['net_vol_flow_5m_und'])
            
            # Calculate volatility-adjusted imbalance
            vol_adjustment = max(0.5, min(2.0, current_iv / 0.20))  # Normalize around 20% IV
            adjusted_imbalance = net_vol_flow * vol_adjustment
            
            # Scale to meaningful range
            vabai = np.tanh(adjusted_imbalance / 100000) * 100  # Bounded between -100 and 100
            
            return vabai
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating VABAI: {e}")
            raise ValueError(f"CRITICAL: VABAI calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    def _calculate_aofm_optimized(self, options_df: pd.DataFrame, underlying_data: Dict) -> float:
        """Calculate Aggressive Order Flow Momentum (simplified)"""
        try:
            # FAIL-FAST: Extract flow momentum data - NO FAKE DEFAULTS ALLOWED
            if 'net_vol_flow_5m_und' not in underlying_data:
                raise ValueError("CRITICAL: net_vol_flow_5m_und missing from underlying_data - cannot calculate AOFM without real 5m flow data!")
            net_vol_flow_5m = float(underlying_data['net_vol_flow_5m_und'])

            if 'net_vol_flow_15m_und' not in underlying_data:
                raise ValueError("CRITICAL: net_vol_flow_15m_und missing from underlying_data - cannot calculate AOFM without real 15m flow data!")
            net_vol_flow_15m = float(underlying_data['net_vol_flow_15m_und'])
            
            # Calculate momentum acceleration
            if abs(net_vol_flow_15m) > 0.001:
                momentum_ratio = net_vol_flow_5m / net_vol_flow_15m
            else:
                momentum_ratio = 0.0
            
            # Calculate momentum change from previous period
            momentum_change = momentum_ratio - self._previous_aofm
            self._previous_aofm = momentum_ratio
            
            # AOFM score
            aofm = momentum_change * 50  # Scale to reasonable range
            
            return self._bound_value(aofm, -100, 100)
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating AOFM: {e}")
            raise ValueError(f"CRITICAL: AOFM calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    def _calculate_lidb_optimized(self, options_df: pd.DataFrame, underlying_data: Dict) -> float:
        """Calculate Liquidity-Implied Directional Bias (simplified)"""
        try:
            # FAIL-FAST: Extract directional flow data - NO FAKE DEFAULTS ALLOWED
            if 'net_cust_delta_flow_und' not in underlying_data:
                raise ValueError("CRITICAL: net_cust_delta_flow_und missing from underlying_data - cannot calculate LIDB without real delta flow data!")
            net_delta_flow = float(underlying_data['net_cust_delta_flow_und'])

            if 'day_volume' not in underlying_data:
                raise ValueError("CRITICAL: day_volume missing from underlying_data - cannot calculate LIDB without real volume data!")
            total_volume = float(underlying_data['day_volume'])

            if total_volume < self.LIQUIDITY_THRESHOLD:
                raise ValueError(f"CRITICAL: Insufficient liquidity (volume={total_volume}) - cannot calculate meaningful LIDB signal!")
            
            # Calculate liquidity-adjusted directional bias
            raw_bias = net_delta_flow / max(total_volume, 1.0)
            
            # Apply momentum component
            bias_change = raw_bias - self._previous_lidb
            self._previous_lidb = raw_bias
            
            # LIDB score with momentum
            lidb = (raw_bias * 0.7 + bias_change * 0.3) * 1000  # Scale to meaningful range
            
            return self._bound_value(lidb, -100, 100)
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating LIDB: {e}")
            raise ValueError(f"CRITICAL: LIDB calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    # =============================================================================
    # UTILITY METHODS
    # =============================================================================
    
    def _get_default_advanced_metrics(self) -> AdvancedOptionsMetrics:
        """Return default advanced metrics on error"""
        return AdvancedOptionsMetrics(
            lwpai=0.0,
            vabai=0.0,
            aofm=0.0,
            lidb=0.0,
            spread_to_volatility_ratio=0.0,
            theoretical_price_deviation=0.0
        )
    
    def calculate_rolling_correlation(self, series1: pd.Series, series2: pd.Series, window: int = 20) -> float:
        """Calculate rolling correlation between two series"""
        try:
            if len(series1) < window or len(series2) < window:
                raise ValueError(f"CRITICAL: Insufficient data for rolling correlation - need at least {window} points, got {len(series1)} and {len(series2)}!")

            # Align series and calculate correlation
            aligned_data = pd.concat([series1, series2], axis=1).dropna()

            if len(aligned_data) < window:
                raise ValueError(f"CRITICAL: Insufficient aligned data for rolling correlation - need at least {window} points, got {len(aligned_data)}!")

            correlation = aligned_data.iloc[:, 0].rolling(window=window).corr(aligned_data.iloc[:, 1]).iloc[-1]

            if pd.isna(correlation):
                raise ValueError("CRITICAL: Rolling correlation calculation resulted in NaN - cannot return fake 0.0!")

            return float(correlation)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating rolling correlation: {e}")
            raise ValueError(f"CRITICAL: Rolling correlation calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    def calculate_volatility_percentile(self, current_iv: float, historical_iv_series: pd.Series, window: int = 252) -> float:
        """Calculate volatility percentile ranking"""
        try:
            if historical_iv_series.empty or len(historical_iv_series) < 10:
                return 50.0  # Neutral percentile
            
            # Use recent window for percentile calculation
            recent_iv = historical_iv_series.tail(window)
            
            # Calculate percentile rank
            percentile = stats.percentileofscore(recent_iv, current_iv)
            
            return self._bound_value(percentile, 0.0, 100.0)
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating volatility percentile: {e}")
            raise ValueError(f"CRITICAL: Volatility percentile calculation failed - cannot return fake 50.0 value! Error: {e}") from e
    
    def calculate_price_momentum_score(self, price_series: pd.Series, periods: List[int] = [5, 10, 20]) -> float:
        """Calculate multi-period price momentum score"""
        try:
            if len(price_series) < max(periods):
                raise ValueError(f"CRITICAL: Insufficient price data for momentum calculation - need at least {max(periods)} points, got {len(price_series)}!")

            momentum_scores = []

            for period in periods:
                if len(price_series) >= period:
                    if price_series.iloc[-period] <= 0:
                        raise ValueError(f"CRITICAL: Invalid price data at period {period} - price must be positive!")
                    # Calculate period return
                    period_return = (price_series.iloc[-1] / price_series.iloc[-period] - 1) * 100
                    momentum_scores.append(period_return)

            if not momentum_scores:
                raise ValueError("CRITICAL: No valid momentum scores calculated - cannot return fake 0.0!")

            # Weighted average (shorter periods get higher weight)
            weights = [1.0 / (i + 1) for i in range(len(momentum_scores))]
            weighted_momentum = np.average(momentum_scores, weights=weights)

            return self._bound_value(weighted_momentum, -100, 100)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating price momentum score: {e}")
            raise ValueError(f"CRITICAL: Price momentum calculation failed - cannot return fake 0.0 value! Error: {e}") from e
    
    def calculate_volume_profile_score(self, volume_series: pd.Series, current_volume: float, window: int = 20) -> float:
        """Calculate volume profile score relative to recent history"""
        try:
            if len(volume_series) < window:
                return 50.0  # Neutral score
            
            # Use recent window for comparison
            recent_volume = volume_series.tail(window)
            
            # Calculate percentile rank of current volume
            volume_percentile = stats.percentileofscore(recent_volume, current_volume)
            
            return self._bound_value(volume_percentile, 0.0, 100.0)
            
        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating volume profile score: {e}")
            raise ValueError(f"CRITICAL: Volume profile calculation failed - cannot return fake 50.0 value! Error: {e}") from e
    
    def calculate_options_skew_indicator(self, options_df: pd.DataFrame, underlying_price: float) -> float:
        """Calculate options skew indicator"""
        try:
            if options_df.empty:
                raise ValueError("CRITICAL: options_df is empty - cannot calculate skew without options data!")

            if 'implied_volatility' not in options_df.columns:
                raise ValueError("CRITICAL: implied_volatility column missing from options_df - cannot calculate skew without IV data!")

            # Separate calls and puts
            calls = options_df[options_df['option_type'] == 'call']
            puts = options_df[options_df['option_type'] == 'put']

            if calls.empty:
                raise ValueError("CRITICAL: No call options found - cannot calculate skew without call data!")

            if puts.empty:
                raise ValueError("CRITICAL: No put options found - cannot calculate skew without put data!")

            # Find ATM options (closest to underlying price)
            calls['moneyness'] = abs(calls['strike'] - underlying_price)
            puts['moneyness'] = abs(puts['strike'] - underlying_price)

            atm_call_iv = calls.loc[calls['moneyness'].idxmin(), 'implied_volatility']
            atm_put_iv = puts.loc[puts['moneyness'].idxmin(), 'implied_volatility']

            if pd.isna(atm_call_iv) or pd.isna(atm_put_iv):
                raise ValueError("CRITICAL: ATM implied volatility is NaN - cannot calculate skew with invalid IV data!")

            # Calculate skew (put IV - call IV)
            skew = atm_put_iv - atm_call_iv

            return float(skew)

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating options skew indicator: {e}")
            raise ValueError(f"CRITICAL: Options skew calculation failed - cannot return fake 0.0 value! Error: {e}") from e

# Export the consolidated calculator
__all__ = ['SupplementaryMetrics', 'AdvancedOptionsMetrics']
