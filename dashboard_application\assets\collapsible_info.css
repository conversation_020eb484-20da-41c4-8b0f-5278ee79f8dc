/**
 * Collapsible Info Sections CSS for AI Dashboard
 * ==============================================
 *
 * Styling for collapsible information sections using HTML details/summary
 */

/* Details/Summary styling for collapsible sections */
details {
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

details summary {
    cursor: pointer !important;
    list-style: none !important;
    outline: none !important;
    user-select: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* Remove default arrow */
details summary::-webkit-details-marker {
    display: none;
}

details summary::marker {
    display: none;
}

/* Add custom indicator */
details summary::after {
    content: "ℹ️";
    font-size: 0.8em;
    margin-left: 8px;
    opacity: 0.6;
    transition: all 0.3s ease;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

details[open] summary::after {
    content: "📖";
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

details summary:hover {
    opacity: 0.8 !important;
    transform: translateY(-1px);
}

details summary:hover::after {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

details summary:active {
    transform: translateY(0px);
}

/* Collapsible content styling */
details div {
    overflow: hidden;
    animation: slideDown 0.3s ease-in-out;
}

details div p {
    margin: 0;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    font-size: 0.8rem;
}

/* Animation for opening */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
}

/* Enhanced hover effects for card headers */
.card-header:has(details) {
    transition: all 0.3s ease;
}

.card-header:has(details):hover {
    background: rgba(255, 255, 255, 0.02) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    details div p {
        font-size: 0.75rem;
        padding: 10px 12px;
    }

    details summary::after {
        font-size: 0.7em;
        margin-left: 6px;
    }
}

/* Dark theme specific adjustments */
.ai-dashboard-container details div p {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Ensure proper z-index for collapsible content */
details {
    position: relative;
    z-index: 10;
}

/* Smooth transitions for all interactive elements */
details, details summary, details div {
    transition: all 0.3s ease;
}
