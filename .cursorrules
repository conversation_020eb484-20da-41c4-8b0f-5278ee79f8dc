# Elite Options System v2.5 - Project Intelligence

## AI Dashboard Architecture Patterns

### Modular Layout System
- **Enhanced 3-Row Design**: Row 1 (Trade Recommendations), Row 2 (Metrics), Row 3 (System Health)
- **Specialized Panel Modules**: Each row has dedicated layout files for clean separation
- **Pydantic-First Validation**: All dashboard components use consistent data validation
- **Market Regime MOE Integration**: Persistent market regime monitoring across all components

### File Consolidation Strategy
- **Legacy File Removal**: `layouts.py` (1446 lines) identified as redundant legacy code
- **Duplicate Elimination**: `ai_hub_layout.py` superseded by `enhanced_ai_hub_layout.py`
- **MOE System Consolidation**: Merge decision formulas and confluence patterns into `elite_moe_system.py`
- **Target Architecture**: Reduce from 17 files to 12 optimized files

### Critical Implementation Paths
- **Core Architecture Files**: `enhanced_ai_hub_layout.py`, `components.py`, `visualizations.py`
- **Modular Components**: `layouts_panels.py`, `layouts_metrics.py`, `layouts_health.py`, `layouts_regime.py`
- **AI Learning System**: Keep 3-file structure with potential modularization of large components
- **Integration Layer**: `callbacks.py` critical for dashboard functionality

## User Preferences & Workflow
- **Knowledge Graph Authority**: All decisions tracked through Persistent Knowledge Graph MCP
- **Memory Bank Updates**: Comprehensive documentation of architectural changes
- **Pydantic-First Approach**: Consistent validation patterns across all components
- **Modular Design**: Clean separation of concerns for maintainability

## Project-Specific Patterns
- **EOTS Integration**: Elite Options Trading System with advanced analytics
- **MOE Systems**: Market of Experts with confluence pattern detection
- **AI Learning**: Continuous learning loop with prediction validation
- **Dashboard Theming**: Figma-inspired design system integration

## Known Challenges
- **File Redundancy**: Multiple overlapping layout files requiring consolidation
- **Large File Management**: Some components exceed optimal size (e.g., 775-line learning manager)
- **Legacy Code**: Outdated files that need careful removal to avoid breaking dependencies
- **Integration Complexity**: Multiple AI systems requiring careful coordination

## Evolution of Project Decisions
- **Phase 1**: Core system foundation with comprehensive analytics
- **Phase 2**: Figma design system integration and visual transformation
- **Phase 3**: AI dashboard architecture cleanup and consolidation (CURRENT)
- **Next Phase**: Performance optimization and documentation completion

## Tool Usage Patterns
- **Memory Bank**: Central documentation system for project state
- **Knowledge Graph**: Persistent intelligence and decision tracking
- **Pydantic Validation**: Type safety and data consistency
- **Modular Architecture**: Component-based design for scalability