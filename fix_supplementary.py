#!/usr/bin/env python3
"""Quick fix for supplementary_metrics.py duplicate class issue"""

import re

def fix_supplementary_metrics():
    file_path = 'core_analytics_engine/eots_metrics/supplementary_metrics.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split into lines
    lines = content.split('\n')
    new_lines = []
    skip_lines = False
    
    for i, line in enumerate(lines):
        # Keep the header comment
        if line.strip() == '# core_analytics_engine/eots_metrics/supplementary_metrics.py':
            new_lines.append(line)
        # Skip the first class definition until we hit the docstring
        elif line.startswith('class AdvancedOptionsMetrics(BaseModel):') and not skip_lines:
            skip_lines = True
            continue
        elif skip_lines and '"""' in line and 'EOTS Supplementary Metrics' in line:
            skip_lines = False
            new_lines.append(line)
        elif not skip_lines:
            new_lines.append(line)
    
    # Write back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print("Fixed duplicate class definition")

if __name__ == '__main__':
    fix_supplementary_metrics()
